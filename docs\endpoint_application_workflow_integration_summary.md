# 终端申请工作流整合接口设计总结

## 背景

原有系统中存在两个独立的接口：
1. `GetEndpointApplyDetail` - 获取申请详情
2. `GetWorkflowSteps` - 获取工作流步骤

前端需要调用多个接口才能获得完整的工作流信息，增加了复杂性和网络开销。

## 设计目标

创建一个整合接口 `GetWorkflowDetailWithSteps`，提供：
- 申请的完整详情信息
- 工作流的所有步骤状态
- 每个步骤的表单字段配置
- 每个步骤已填写的数据
- 当前可执行的操作

## 核心功能

### 1. 工作流步骤状态追踪
- **状态判断**: 准确判断每个步骤的状态（completed/rejected/current/waiting）
- **到达检测**: 通过 `is_reached` 字段标识流程是否已到达该节点
- **进度可视化**: 支持前端渲染进度条和状态图标

### 2. 动态表单配置
- **字段类型**: 支持多种表单字段类型（text/textarea/select/file/number等）
- **验证规则**: 提供字段验证配置（必填、长度、格式等）
- **选项配置**: 支持下拉选择字段的选项配置
- **描述信息**: 提供字段说明和占位符

### 3. 数据回显
- **已填数据**: 显示每个步骤已填写的表单数据
- **数据来源**: 从申请的扩展字段中提取相关数据
- **格式转换**: 将存储格式转换为前端友好的格式

### 4. 操作控制
- **当前操作**: 只显示当前步骤可执行的操作
- **权限控制**: 确保用户只能查看自己代理商的申请
- **状态机集成**: 与现有状态机系统无缝集成

## 技术实现

### 1. 接口层 (Handler)
```go
// 新增整合接口
func (e endpointApply) GetWorkflowDetailWithSteps(c *gin.Context) {
    // 参数验证
    // 权限检查
    // 调用服务层
    // 返回结果
}
```

### 2. 服务层 (Service)
```go
// 主要整合方法
func (e endpointApplyService) GetWorkflowDetailWithSteps(
    c *gin.Context, 
    id uint, 
    agencyID uint, 
    agencyLevel int
) (*api.WorkflowDetailWithStepsResp, error) {
    // 获取申请详情
    // 构建工作流步骤
    // 获取可用操作
    // 组装返回数据
}
```

### 3. 工作流步骤构建
```go
// 带表单字段的步骤构建
func (e endpointApplyService) buildWorkflowStepsWithForm(
    currentState int, 
    workflow *statemachine.WorkflowConfig, 
    apply *model.EndpointApplication
) ([]*api.WorkflowStepWithForm, error) {
    // 构建主流程路径
    // 生成步骤信息
    // 配置表单字段
    // 提取已填数据
}
```

### 4. 表单字段配置
```go
// 根据状态类型配置表单字段
func (e endpointApplyService) getFormFieldsForState(
    stateNode *statemachine.StateNode
) ([]api.FormFieldConfig, error) {
    // 审核状态: 审核备注
    // 物料状态: 物料清单、是否需要物料
    // 核销状态: 核销表、租赁合同、年租金
    // 物料完成状态: 设计图、装修图、视频
}
```

## 数据结构设计

### 1. 主响应结构
```go
type WorkflowDetailWithStepsResp struct {
    ApplicationDetail *EndpointApplicationDetailResp `json:"application_detail"`
    WorkflowSteps     []*WorkflowStepWithForm        `json:"workflow_steps"`
    AvailableActions  []ActionInfo                   `json:"available_actions"`
    WorkflowInfo      *WorkflowBasicInfo             `json:"workflow_info"`
}
```

### 2. 步骤结构
```go
type WorkflowStepWithForm struct {
    State      int                    `json:"state"`
    Name       string                 `json:"name"`
    Type       string                 `json:"type"`
    Order      int                    `json:"order"`
    Status     string                 `json:"status"`
    StatusText string                 `json:"status_text"`
    IsReached  bool                   `json:"is_reached"`
    FormFields []FormFieldConfig      `json:"form_fields"`
    FormData   map[string]interface{} `json:"form_data"`
    Actions    []ActionInfo           `json:"actions"`
}
```

### 3. 表单字段配置
```go
type FormFieldConfig struct {
    Name        string           `json:"name"`
    Label       string           `json:"label"`
    Type        string           `json:"type"`
    Required    bool             `json:"required"`
    Options     []FieldOption    `json:"options"`
    Validation  *FieldValidation `json:"validation"`
    Description string           `json:"description"`
    Placeholder string           `json:"placeholder"`
}
```

## 优势分析

### 1. 性能优化
- **减少请求**: 一次请求获取所有需要的数据
- **网络优化**: 减少网络往返次数
- **缓存友好**: 单一接口更容易实现缓存策略

### 2. 开发效率
- **前端简化**: 前端只需调用一个接口
- **数据一致性**: 避免多接口数据不一致问题
- **维护便利**: 集中的数据逻辑更容易维护

### 3. 用户体验
- **加载速度**: 更快的页面加载速度
- **状态同步**: 实时的工作流状态展示
- **操作引导**: 清晰的下一步操作指引

### 4. 扩展性
- **字段扩展**: 易于添加新的表单字段类型
- **状态扩展**: 支持新的工作流状态
- **配置驱动**: 通过配置控制表单行为

## 使用场景

### 1. 工作流进度页面
显示申请在整个工作流中的当前位置和进度。

### 2. 表单填写页面
根据当前状态动态渲染需要填写的表单字段。

### 3. 审核操作页面
显示可执行的操作和相关的表单字段。

### 4. 历史记录查看
查看每个步骤的历史数据和操作记录。

## 路由配置

```go
// Agency端路由
r.GET("/:id/workflow-detail", e.handler.GetWorkflowDetailWithSteps)
```

## 权限控制

- 用户只能查看自己代理商的申请
- 一级代理商检查 `top_agency` 字段
- 二级代理商检查 `second_agency` 字段

## 兼容性

- 保持原有接口不变，确保向后兼容
- 新接口作为增强功能提供
- 可以逐步迁移到新接口

## 后续优化建议

1. **缓存策略**: 对工作流配置和表单字段配置实现缓存
2. **异步加载**: 对大量数据实现分页或异步加载
3. **实时更新**: 集成WebSocket实现实时状态更新
4. **移动端适配**: 针对移动端优化数据结构
5. **国际化**: 支持多语言的字段标签和描述

## 总结

这个整合接口通过合并原有的两个接口，提供了更完整、更高效的工作流信息获取方式。它不仅减少了网络请求，还提供了丰富的表单配置和数据回显功能，为前端开发提供了强大的支持，显著提升了用户体验和开发效率。

package endpoint_application

import (
	"marketing/internal/api"
	"marketing/internal/model"
	"marketing/internal/pkg/types"
)

type CreatedEndpointApplyReq struct {
	TopAgency       uint            `json:"top_agency" form:"top_agency" binding:"required"`
	SecondAgency    uint            `json:"second_agency" form:"second_agency"`
	Name            string          `json:"name" form:"name"`
	Type            int             `json:"type" form:"type"`
	Province        int             `json:"province" form:"province"`
	City            int             `json:"city" form:"city"`
	District        int             `json:"district" form:"district"`
	Address         string          `json:"address" form:"address"`
	Lng             string          `json:"lng" form:"lng"` //高德坐标系自己转换
	Lat             string          `json:"lat" form:"lat"`
	ChannelLevel    uint8           `json:"channel_level" form:"channel_level"`
	Blng            string          `json:"blng" form:"blat"`
	Blat            string          `json:"blat" form:"blat"`
	PolicyID        int             `json:"policy_id" form:"policy_id"`
	Phone           string          `json:"phone" form:"phone" binding:"phone"`
	Manager         string          `json:"manager" form:"manager"`
	ApplicationYear int             `json:"application_year"`
	Investor        string          `json:"investor" form:"investor"`                 //投资者
	InvestorPhone   string          `json:"investor_phone" form:"investor_phone"`     //投资者手机号
	Position        string          `json:"position" form:"position"`                 // 所处地段
	EndpointArea    string          `json:"endpoint_area" form:"endpoint_area"`       // 终端面积
	Pics            []types.OssPath `json:"pics" form:"pics"`                         // 装修前实景图
	PicsExternal    []types.OssPath `json:"pics_external" form:"pics_external"`       // 外部环境图
	PicsInternal    []types.OssPath `json:"pics_internal" form:"pics_internal"`       // 内部环境图
	PicsDesign      []types.OssPath `json:"pics_design" form:"pics_design"`           // 平面图
	ExpectOpenTime  string          `json:"expect_open_time" form:"expect_open_time"` // 期望开盘时间
	Extend          map[string]any  `json:"extend" form:"extend"`
	State           int
}

type EndpointApplicationListReq struct {
	api.PaginationParams
	PolicyID     int    `json:"policy_id" form:"policy_id"`
	State        int    `json:"state" form:"state"`
	Name         string `json:"name" form:"name"`
	Type         int    `json:"type" form:"type"`
	Code         int    `json:"code" form:"code"`
	TopAgency    int    `json:"top_agency" form:"top_agency"`
	SecondAgency int    `json:"second_agency" form:"second_agency"`
}

type EndpointApplicationListResp struct {
	model.EndpointApplication
	PicsArr            []types.OssPath  `json:"pics" gorm:"-"`
	PicsExternalArr    []types.OssPath  `json:"pics_external" gorm:"-"`
	PicsInternalArr    []types.OssPath  `json:"pics_internal" gorm:"-"`
	PicsDesignArr      []types.OssPath  `json:"pics_design" gorm:"-"`
	ExtendMap          types.JSONMap    `json:"extend" gorm:"-"`
	Code               int              `json:"code"`
	EndpointID         int              `json:"endpoint_id"`
	EndpointCode       string           `json:"endpoint_code"`
	CreatedAtFormatted types.CustomTime `json:"created_at_formatted"`
	TopAgencyName      string           `json:"top_agency_name"`
	SecondAgencyName   string           `json:"second_agency_name"`
	EndpointTypeName   string           `json:"endpoint_type_name"`
	StateName          string           `json:"state_name"`
	//NextStateName      string           `json:"next_state_name"`
	// 工作流相关字段
	AvailableActions []ActionInfo `json:"available_actions" gorm:"-"`  // 当前状态可执行的操作
	CurrentStateInfo *StateInfo   `json:"current_state_info" gorm:"-"` // 当前状态详细信息
}

// StateInfo 状态信息
type StateInfo struct {
	ID   int    `json:"id"`   // 状态ID
	Name string `json:"name"` // 状态名称
	Type string `json:"type"` // 状态类型
}

// EndpointApplicationDetailResp 终端申请详情响应
type EndpointApplicationDetailResp struct {
	*EndpointApplicationListResp
	// 可以添加详情页特有的字段
	PostbackData *PostbackData `json:"postback_data,omitempty"` // 回传数据
	MaterialData *MaterialData `json:"material_data,omitempty"` // 物料数据
	AuditHistory []AuditRecord `json:"audit_history,omitempty"` // 审核历史
	WorkflowInfo *WorkflowInfo `json:"workflow_info"`           // 工作流信息
}

// PostbackData 回传数据
type PostbackData struct {
	WriteOffTable      types.OssPath    `json:"write_off_table"`
	LeaseContract      types.OssPath    `json:"lease_contract"`
	AnnualRent         float64          `json:"annual_rent"`
	DesignRenderings   []types.OssPath  `json:"design_renderings"`
	RenovationPhotos   []types.OssPath  `json:"renovation_photos"`
	RenovationVideos   []types.OssPath  `json:"renovation_videos"`
	Diploma            []types.OssPath  `json:"diploma"`
	EndpointGroupPhoto []types.OssPath  `json:"endpoint_group_photo"`
	ConfirmDate        types.CustomTime `json:"confirm_date"`
	Extend             types.JSONMap    `json:"extend"`
}

// MaterialData 物料数据
type MaterialData struct {
	MaterialList []MaterialItem `json:"material_list"`
	TotalAmount  float64        `json:"total_amount"`
	RawDetail    string         `json:"raw_detail,omitempty"`    // 原始字符串格式的物料详情
	IsRawFormat  bool           `json:"is_raw_format,omitempty"` // 是否为原始字符串格式
}

// MaterialItem 物料项
type MaterialItem struct {
	ID       uint    `json:"id"`
	Name     string  `json:"name"`
	Quantity int     `json:"quantity"`
	Price    float64 `json:"price"`
	Amount   float64 `json:"amount"`
}

// AuditRecord 审核记录
type AuditRecord struct {
	ID          uint   `json:"id"`
	Action      string `json:"action"`
	ActionName  string `json:"action_name"`
	FromState   int    `json:"from_state"`
	ToState     int    `json:"to_state"`
	Remark      string `json:"remark"`
	AuditorID   uint   `json:"auditor_id"`
	AuditorName string `json:"auditor_name"`
	CreatedAt   string `json:"created_at"`
}

// WorkflowInfo 工作流信息
type WorkflowInfo struct {
	Name         string `json:"name"`          // 工作流名称
	Slug         string `json:"slug"`          // 工作流标识
	CurrentState int    `json:"current_state"` // 当前状态
	StartState   int    `json:"start_state"`   // 开始状态
}

type AuditApplyReq struct {
	ID          uint           `json:"id" form:"id"`
	State       int            `json:"state" form:"state" binding:"required"`
	Action      string         `json:"action" form:"action" binding:"required"`
	AuditAdvice string         `json:"audit_advice" form:"audit_advice"` // 建议
	Extend      map[string]any `json:"extend" form:"extend"`
}

type EndpointMaterialSupport struct {
	ID               uint    `json:"id"`
	Name             string  `json:"name"`
	Pic              string  `json:"pic"`
	Price            float64 `json:"price"`
	ProductionNumber string  `json:"production_number"`
	Thumbnail        string  `json:"thumbnail"`
	Num              int     `json:"num"`
}

type PostbackEndpointApplyReq struct {
	ID                 uint            `json:"id" form:"id"`
	Remark             string          `json:"remark" form:"remark"`
	Extend             map[string]any  `json:"extend" form:"extend"`
	WriteOffTable      types.OssPath   `json:"write_off_table" form:"write_off_table"`
	LeaseContract      types.OssPath   `json:"lease_contract" form:"lease_contract"`
	AnnualRent         float64         `json:"annual_rent" form:"annual_rent"`
	DesignRenderings   []types.OssPath `json:"design_renderings" form:"design_renderings"`
	RenovationPhotos   []types.OssPath `json:"renovation_photos" form:"renovation_photos"`
	RenovationVideos   []types.OssPath `json:"renovation_videos" form:"renovation_videos"`
	Diploma            []types.OssPath `json:"diploma" form:"diploma"`
	EndpointGroupPhoto []types.OssPath `json:"endpoint_group_photo" form:"endpoint_group_photo"`
}

// WriteOffReq 核销请求
type WriteOffReq struct {
	ID               uint           `json:"id" form:"id"`
	State            int            `json:"state" form:"state" binding:"required"` // 审核状态
	Action           string         `json:"action" form:"action" binding:"required"`
	WriteOffAdvice   string         `json:"write_off_advice" form:"write_off_advice"`     // 核销建议
	Pay              string         `json:"pay" form:"pay"`                               // 支付金额
	BagSupportAmount float64        `json:"bag_support_amount" form:"bag_support_amount"` // 书包支持金额
	Extend           map[string]any `json:"extend" form:"extend"`                         // 扩展字段
}

// ChannelAuditReq 渠道审核请求
type ChannelAuditReq struct {
	ID            uint           `json:"id" form:"id"`
	State         int            `json:"state" form:"state" binding:"required"` // 审核状态
	Action        string         `json:"action" form:"action" binding:"required"`
	ChannelPhotos []string       `json:"channel_photos" form:"channel_photos"` // 渠道照片
	ChannelAdvice string         `json:"channel_advice" form:"channel_advice"` // 渠道建议
	RealOpenTime  int64          `json:"real_open_time" form:"real_open_time"` // 真实开业时间
	Extend        map[string]any `json:"extend" form:"extend"`                 // 扩展字段
}

// TerminateReq 建店终止请求
type TerminateReq struct {
	ID              uint           `json:"id" form:"id"`
	TerminateReason string         `json:"terminate_reason" form:"terminate_reason" binding:"required"` // 终止原因
	Remark          string         `json:"remark" form:"remark"`                                        // 备注
	Extend          map[string]any `json:"extend" form:"extend"`                                        // 扩展字段
}

type LatestEndpointImageResp struct {
	AutoTime   types.CustomTime `json:"auto_time"`
	ManualTime types.CustomTime `json:"manual_time"`
}

// AccountRecordReq 入账请求
type AccountRecordReq struct {
	ID            uint   `json:"id" form:"id"`                         // 分期ID
	SupportStatus uint8  `json:"support_status" form:"support_status"` // 入账状态：1-已入账，2-中止
	RecordRemark  string `json:"record_remark" form:"record_remark"`   // 入账备注
}

// InstallmentListReq 分期列表请求
type InstallmentListReq struct {
	api.PaginationParams
	ApplicationID uint   `json:"application_id" form:"application_id"` // 申请ID
	Year          uint16 `json:"year" form:"year"`                     // 年份
	Month         uint8  `json:"month" form:"month"`                   // 月份
	SupportStatus *uint8 `json:"support_status" form:"support_status"` // 入账状态：0-未入账，1-已入账，2-中止
}

// InstallmentsByApplicationReq 根据申请ID获取分期列表请求（不分页）
type InstallmentsByApplicationReq struct {
	ApplicationID uint `json:"application_id" form:"application_id" binding:"required"` // 申请ID
}

// InstallmentListResp 分期列表响应
type InstallmentListResp struct {
	ID                 uint             `json:"id"`
	ApplicationID      uint             `json:"application_id"`
	ApplicationName    string           `json:"application_name"` // 申请名称
	Year               uint16           `json:"year"`
	Month              uint8            `json:"month"`
	Date               types.DateOnly   `json:"date"`
	Fee                uint             `json:"fee"`                  // 支持金额（分）
	StaffFee           *uint            `json:"staff_fee"`            // 人员支持金额（分）
	SupportStatus      uint8            `json:"support_status"`       // 入账状态
	StaffSupportStatus uint8            `json:"staff_support_status"` // 支持人员入账状态
	StaffRecordRemark  string           `json:"staff_record_remark"`
	RecordedBy         uint             `json:"recorded_by"`   // 入账人
	RecordedAt         types.CustomTime `json:"recorded_at"`   // 入账时间
	RecordRemark       string           `json:"record_remark"` // 入账备注
	CreatedAt          types.CustomTime `json:"created_at"`
	UpdatedAt          types.CustomTime `json:"updated_at"`

	// 扩展字段
	SupportStatusName string `json:"support_status_name"` // 入账状态名称
	RecordedByName    string `json:"recorded_by_name"`    // 入账人姓名
}

// WorkflowDetailWithStepsResp 工作流详情和步骤整合响应
type WorkflowDetailWithStepsResp struct {
	// 申请基本信息
	ApplicationDetail *EndpointApplicationDetailResp `json:"application_detail"`

	// 工作流步骤信息
	WorkflowSteps []*WorkflowStepWithForm `json:"workflow_steps"`

	// 当前可执行的操作
	AvailableActions []ActionInfo `json:"available_actions"`

	// 工作流基本信息
	WorkflowInfo *WorkflowBasicInfo `json:"workflow_info"`
}

// WorkflowStepWithForm 带表单字段的工作流步骤
type WorkflowStepWithForm struct {
	State      int                    `json:"state"`       // 状态ID
	Name       string                 `json:"name"`        // 状态名称
	Type       string                 `json:"type"`        // 状态类型
	Order      int                    `json:"order"`       // 步骤顺序
	Status     string                 `json:"status"`      // 步骤状态: completed, rejected, current, waiting
	StatusText string                 `json:"status_text"` // 状态描述文本
	IsReached  bool                   `json:"is_reached"`  // 是否已到达此节点

	// 表单字段配置
	FormFields []FormFieldConfig      `json:"form_fields"` // 该步骤需要的表单字段

	// 当前步骤的数据（如果已填写）
	FormData   map[string]interface{} `json:"form_data"`   // 该步骤已填写的数据

	// 可执行的操作
	Actions    []ActionInfo           `json:"actions"`     // 该步骤可执行的操作
}

// FormFieldConfig 表单字段配置
type FormFieldConfig struct {
	Name        string      `json:"name"`         // 字段名称
	Label       string      `json:"label"`        // 字段标签
	Type        string      `json:"type"`         // 字段类型: text, textarea, select, file, date, number
	Required    bool        `json:"required"`     // 是否必填
	Options     []FieldOption `json:"options"`    // 选项（用于select类型）
	Validation  *FieldValidation `json:"validation"` // 验证规则
	Description string      `json:"description"`  // 字段描述
	Placeholder string      `json:"placeholder"`  // 占位符
}

// FieldOption 字段选项
type FieldOption struct {
	Value string `json:"value"` // 选项值
	Label string `json:"label"` // 选项标签
}

// FieldValidation 字段验证规则
type FieldValidation struct {
	MinLength *int    `json:"min_length,omitempty"` // 最小长度
	MaxLength *int    `json:"max_length,omitempty"` // 最大长度
	Pattern   *string `json:"pattern,omitempty"`    // 正则表达式
	Min       *float64 `json:"min,omitempty"`       // 最小值（数字类型）
	Max       *float64 `json:"max,omitempty"`       // 最大值（数字类型）
}

// WorkflowBasicInfo 工作流基本信息
type WorkflowBasicInfo struct {
	Name       string `json:"name"`        // 工作流名称
	Slug       string `json:"slug"`        // 工作流标识
	StartState int    `json:"start_state"` // 起始状态
	TotalSteps int    `json:"total_steps"` // 总步骤数
}

# 终端申请工作流操作记录接口修改说明

## 修改概述

根据用户反馈，将原来返回表单字段配置的接口修改为返回实际操作记录数据的接口。用户的具体需求是：

> "比如我现在提交了申请 到第二步 是审核 100的状态或者-100的状态 我会在 audit_approved 下面返回 审核的意见 审核的状态 审核人审核时间等等 以此类推"

## 主要修改内容

### 1. API 响应结构修改

**修改前：**
```go
type WorkflowStepWithForm struct {
    // ... 其他字段
    FormFields []FormFieldConfig      `json:"form_fields"` // 表单字段配置
    FormData   map[string]interface{} `json:"form_data"`   // 已填写的数据
    // ... 其他字段
}
```

**修改后：**
```go
type WorkflowStepWithForm struct {
    // ... 其他字段
    OperationData map[string]interface{} `json:"operation_data"` // 实际操作记录数据
    // ... 其他字段
}
```

### 2. 服务层逻辑修改

修改了 `getFormDataForState` 方法，现在根据不同的工作流步骤返回实际的操作记录：

#### 审核步骤 (state: 0, 100, -100)
- `audit_man`: 审核人
- `audit_time`: 审核时间
- `audit_advice`: 审核意见
- `audit_result`: 审核结果 (approved/rejected)
- `audit_result_text`: 审核结果文本

#### 物料支持步骤 (state: 100)
- `material_list`: 物料清单（从 endpoint_material_support 表获取）
- `total_amount`: 物料总金额
- `bag_support_amount`: 物料支持金额
- `material_support_time`: 物料支持时间

#### 回传步骤 (state: 200)
- `write_off_table`: 核销表文件路径
- `lease_contract`: 租赁合同文件路径
- `annual_rent`: 年租金
- `confirm_date`: 确认日期
- `design_renderings`: 设计效果图文件路径列表
- `renovation_photos`: 装修实景图文件路径列表
- `renovation_videos`: 装修实景视频文件路径列表
- `diploma`: 证书文件路径列表
- `endpoint_group_photo`: 终端合影文件路径列表

#### 核销步骤 (state: 300, 400, -400)
- `write_off_man`: 核销人
- `write_off_time`: 核销时间
- `write_off_advice`: 核销意见
- `write_off_result`: 核销结果 (approved/rejected)
- `write_off_result_text`: 核销结果文本

#### 渠道审核步骤 (state: 400, 500, -500)
- `channel_audit_man`: 渠道审核人
- `channel_audit_time`: 渠道审核时间
- `channel_audit_advice`: 渠道审核意见
- `channel_audit_result`: 渠道审核结果 (approved/rejected)
- `channel_audit_result_text`: 渠道审核结果文本

### 3. 数据来源

操作记录数据来源于以下数据表：

1. **主表 (endpoint_application)**：
   - 审核相关字段：`audit_man`, `audit_time`, `audit_advice`
   - 核销相关字段：`write_off_man`, `write_off_time`, `write_off_advice`
   - 物料支持金额：`bag_support_amount`

2. **物料支持表 (endpoint_material_support)**：
   - 物料清单详情

3. **回传表 (endpoint_application_postback)**：
   - 回传文件和数据

4. **扩展字段 (extend)**：
   - 渠道审核相关信息（JSON格式存储）

### 4. 时间字段处理

由于使用了自定义的 `types.CustomTime` 类型，需要特殊处理：

```go
auditTime := time.Time(apply.AuditTime)
if !auditTime.IsZero() {
    formData["audit_time"] = auditTime.Format("2006-01-02 15:04:05")
}
```

### 5. 删除的内容

- 删除了 `FormFieldConfig` 结构体
- 删除了 `FieldOption` 结构体
- 删除了 `FieldValidation` 结构体
- 删除了 `getFormFieldsForState` 方法

## 接口使用示例

### 请求
```
GET /agency/endpoint-application/123/workflow-detail
```

### 响应示例
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "application": {
      // ... 申请详情
    },
    "workflow_steps": [
      {
        "state": 0,
        "name": "待审核",
        "type": "audit",
        "order": 1,
        "status": "completed",
        "status_text": "审核通过",
        "is_reached": true,
        "operation_data": {
          "audit_man": "张审核员",
          "audit_time": "2025-01-15 11:30:00",
          "audit_advice": "申请材料齐全，审核通过",
          "audit_result": "approved",
          "audit_result_text": "审核通过"
        },
        "actions": []
      }
    ]
  }
}
```

## 优势

1. **真实数据**：返回实际的操作记录，而不是表单配置
2. **历史追踪**：可以查看每个步骤的具体操作人、时间和结果
3. **数据完整性**：包含所有相关的操作数据
4. **前端友好**：前端可以直接显示操作历史，无需额外请求

## 注意事项

1. 对于未到达的步骤，`operation_data` 为空对象 `{}`
2. 时间格式统一为 `"2006-01-02 15:04:05"`
3. 文件路径以相对路径形式返回
4. 审核结果和核销结果包含 `result` 和 `result_text` 两个字段，便于前端处理

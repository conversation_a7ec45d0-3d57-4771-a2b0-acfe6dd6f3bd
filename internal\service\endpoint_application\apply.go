package endpoint_application

import (
	"bytes"
	"encoding/json"
	"fmt"
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/consts"
	"marketing/internal/dao"
	endpointDao "marketing/internal/dao/endpoint"
	applicationDao "marketing/internal/dao/endpoint_application"
	warrantyDao "marketing/internal/dao/warranty"
	"marketing/internal/model"
	"marketing/internal/pkg/errors"
	"marketing/internal/pkg/log"
	"marketing/internal/pkg/oss"
	"marketing/internal/pkg/statemachine"
	"marketing/internal/pkg/types"
	"marketing/internal/pkg/utils"
	"marketing/internal/service/endpoint_application/processors"
	"marketing/internal/service/endpoint_application/validators"
	"strconv"
	"time"

	"github.com/carmel/gooxml/document"
	"github.com/gin-gonic/gin"
	"github.com/qichengzx/coordtransform"
	"github.com/spf13/cast"
	"go.uber.org/zap"
)

type EndpointApplyService interface {
	CreateEndpointApply(c *gin.Context, req *api.CreatedEndpointApplyReq) error
	UpdateEndpointApply(c *gin.Context, id int, req *api.CreatedEndpointApplyReq) error
	GetEndpointApplyList(c *gin.Context, param *api.EndpointApplicationListReq) ([]*api.EndpointApplicationListResp, int64, error)
	GetEndpointApplyDetail(c *gin.Context, id uint) (*api.EndpointApplicationDetailResp, error)
	GetMaterials(c *gin.Context, name string, categories []int, page, pageSize int) ([]*api.EndpointMaterialSupport, error)
	DownloadWriteOffTable(c *gin.Context, id int) error
	LatestEndpointImage(c *gin.Context, id int) (*api.LatestEndpointImageResp, error)
	SalesAmount(c *gin.Context, id int, years int, endAt int64) (int64, float64, error)

	// TransitionState 状态机相关方法
	TransitionState(c *gin.Context, req *api.StateTransitionReq) error
	AuditApply(c *gin.Context, req *api.AuditApplyReq) error
	MaterialSupport(c *gin.Context, id uint, req []*api.EndpointMaterialSupport) error
	PostbackEndpointApply(c *gin.Context, req *api.PostbackEndpointApplyReq) error
	WriteOff(c *gin.Context, req *api.WriteOffReq) error
	ChannelConfirmation(c *gin.Context, req *api.ChannelAuditReq) error
	RecordConfirmation(c *gin.Context, req *api.AuditApplyReq) error
	Terminate(c *gin.Context, req *api.TerminateReq) error

	// GetAvailableActions 状态机查询方法
	GetAvailableActions(c *gin.Context, id uint) ([]statemachine.Action, error)
	GetNextStates(c *gin.Context, id uint) ([]api.StateInfo, error)

	// GetWorkflowSteps 获取工作流步骤
	GetWorkflowSteps(c *gin.Context, id uint) ([]*WorkflowStep, error)

	// GetWorkflowDetailWithSteps 获取工作流详情和步骤（整合接口）
	GetWorkflowDetailWithSteps(c *gin.Context, id uint, agencyID uint, agencyLevel int) (*api.WorkflowDetailWithStepsResp, error)
}

type endpointApplyService struct {
	repo             applicationDao.EndpointApplyDao
	policyRepo       applicationDao.EndpointPolicyDao
	endpointRepo     endpointDao.EndpointDao
	endpointTypeRepo dao.TypeRepository
	configRepo       dao.ConfigRepository
	subjectRepo      dao.TrainSubjectDao
	materialRepo     dao.MaterialDao
	endpointImageDao endpointDao.ImageDao
	warrantyRepo     warrantyDao.InterfaceWarranty
	workflowDao      applicationDao.WorkflowConfigDao
}

func (e endpointApplyService) GetValidator(name string) (statemachine.Validator, error) {
	switch name {
	case "permission":
		return validators.NewPermissionValidator(), nil
	case "data":
		return validators.NewDataValidator(), nil
	case "policy":
		return validators.NewPolicyValidator(), nil
	default:
		return nil, fmt.Errorf("unknown validator: %s", name)
	}
}

func (e endpointApplyService) ListValidators() []string {
	return []string{}
}

func (e endpointApplyService) GetProcessor(name string) (statemachine.Processor, error) {
	switch name {
	// 兼容旧的处理器名称
	case "audit":
		return processors.NewAuditProcessor(), nil
	case "postback":
		return processors.NewPostbackProcessor(), nil
	case "material":
		return processors.NewMaterialProcessor(), nil
	case "writeoff":
		return processors.NewWriteOffProcessor(), nil
	case "writeoff_confirm":
		return processors.NewWriteOffConfirmProcessor(), nil
	case "record":
		return processors.NewRecordProcessor(), nil
	case "endpoint_hook":
		return processors.NewEndpointHookProcessor(), nil
	case "sms_notification":
		return processors.NewSMSNotificationProcessor(), nil
	case "design_notification":
		return processors.NewDesignNotificationProcessor(), nil
	case "renew":
		return processors.NewRenewProcessor(), nil
	default:
		return nil, fmt.Errorf("unknown processor: %s", name)
	}
}

func (e endpointApplyService) ListProcessors() []string {
	return []string{"approved", "rejected", "postback", "material", "writeoff", "writeoff_confirm", "record", "endpoint_hook", "sms_notification", "design_notification", "renew"}
}

func NewEndpointApplyService(repo applicationDao.EndpointApplyDao,
	policyRepo applicationDao.EndpointPolicyDao,
	endpointRepo endpointDao.EndpointDao,
	endpointTypeRepo dao.TypeRepository,
	configRepo dao.ConfigRepository,
	subjectRepo dao.TrainSubjectDao,
	material dao.MaterialDao,
	endpointImageDao endpointDao.ImageDao,
	warrantyRepo warrantyDao.InterfaceWarranty,
	workflowRepo applicationDao.WorkflowConfigDao,
) EndpointApplyService {

	return &endpointApplyService{
		repo:             repo,
		policyRepo:       policyRepo,
		endpointRepo:     endpointRepo,
		endpointTypeRepo: endpointTypeRepo,
		configRepo:       configRepo,
		subjectRepo:      subjectRepo,
		materialRepo:     material,
		endpointImageDao: endpointImageDao,
		warrantyRepo:     warrantyRepo,
		workflowDao:      workflowRepo,
	}
}

func (e endpointApplyService) GetEndpointApplyList(c *gin.Context, param *api.EndpointApplicationListReq) ([]*api.EndpointApplicationListResp, int64, error) {
	data, total, err := e.repo.GetEndpointApplyList(c, param)
	if err != nil {
		return nil, 0, err
	}
	// 处理代理信息
	agencyIDs := make([]int, 0)
	for _, item := range data {
		if item.TopAgency > 0 {
			agencyIDs = append(agencyIDs, int(item.TopAgency))
		}
		if item.SecondAgency > 0 {
			agencyIDs = append(agencyIDs, int(item.SecondAgency))
		}
	}
	//代理商
	agencies, _ := e.endpointRepo.GetAgencies(c, agencyIDs)
	// 终端类型
	endpointTypes, _ := e.endpointTypeRepo.GetEndpointTypeMap(c)
	for i := range data {
		// 使用通用的 JSON 解析方法
		types.UnmarshalJSONField(data[i].Pics, &data[i].PicsArr)
		types.UnmarshalJSONField(data[i].PicsExternal, &data[i].PicsExternalArr)
		types.UnmarshalJSONField(data[i].PicsInternal, &data[i].PicsInternalArr)
		types.UnmarshalJSONField(data[i].PicsDesign, &data[i].PicsDesignArr)
		types.UnmarshalJSONField(data[i].Extend, &data[i].ExtendMap)
		data[i].TopAgencyName = agencies[data[i].TopAgency]
		data[i].SecondAgencyName = agencies[data[i].SecondAgency]
		data[i].EndpointTypeName = endpointTypes[uint(data[i].Type)]
		data[i].StateName = consts.EndpointApplicationState(data[i].State).String()

		// 添加工作流信息
		currentState := consts.EndpointApplicationState(data[i].State)

		// 根据政策获取状态机
		if stateMachine, err := statemachine.CreateStateMachineForPolicy(data[i].PolicyID, e.workflowDao, e, e); err == nil {
			// 获取可用操作
			if actions, err := stateMachine.GetAvailableActions(currentState); err == nil {
				var actionInfos []api.ActionInfo
				for _, action := range actions {
					actionInfos = append(actionInfos, api.ActionInfo{
						Type:   action.Type,
						Label:  action.Label,
						Config: action.Config,
					})
				}
				data[i].AvailableActions = actionInfos
			}

			// 获取当前状态信息
			if stateInfo, err := stateMachine.GetStateInfo(currentState); err == nil {
				data[i].CurrentStateInfo = &api.StateInfo{
					ID:   int(stateInfo.ID),
					Name: stateInfo.Name,
					Type: stateInfo.Type,
				}
			}
		}
	}
	return data, total, nil
}

// GetEndpointApplyDetail 获取终端申请详情
func (e endpointApplyService) GetEndpointApplyDetail(c *gin.Context, id uint) (*api.EndpointApplicationDetailResp, error) {
	// 获取申请基本信息
	apply, err := e.repo.GetEndpointApplyByID(c, int(id))
	if err != nil {
		return nil, err
	}
	if apply == nil {
		return nil, errors.NewErr("申请不存在")
	}

	// 手动构建详情响应（类似列表逻辑但针对单个记录）
	detail := &api.EndpointApplicationDetailResp{
		EndpointApplicationListResp: &api.EndpointApplicationListResp{
			EndpointApplication: *apply,
		},
	}

	// 处理图片和扩展字段
	if err := json.Unmarshal([]byte(apply.Pics), &detail.PicsArr); err != nil {
		log.Error(err.Error())
	}
	if err := json.Unmarshal([]byte(apply.Extend), &detail.ExtendMap); err != nil {
		log.Error(err.Error())
	}

	// 获取代理商信息
	agencyIDs := []int{}
	if apply.TopAgency > 0 {
		agencyIDs = append(agencyIDs, int(apply.TopAgency))
	}
	if apply.SecondAgency > 0 {
		agencyIDs = append(agencyIDs, int(apply.SecondAgency))
	}
	if len(agencyIDs) > 0 {
		agencies, _ := e.endpointRepo.GetAgencies(c, agencyIDs)
		detail.TopAgencyName = agencies[apply.TopAgency]
		detail.SecondAgencyName = agencies[apply.SecondAgency]
	}

	// 获取终端类型
	if endpointTypes, err := e.endpointTypeRepo.GetEndpointTypeMap(c); err == nil {
		detail.EndpointTypeName = endpointTypes[uint(apply.Type)]
	}

	// 设置状态名称
	detail.StateName = consts.EndpointApplicationState(apply.State).String()

	// 根据政策获取状态机
	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return nil, err
	}

	// 添加工作流信息
	currentState := consts.EndpointApplicationState(apply.State)

	// 获取可用操作
	if actions, err := stateMachine.GetAvailableActions(currentState); err == nil {
		var actionInfos []api.ActionInfo
		for _, action := range actions {
			actionInfos = append(actionInfos, api.ActionInfo{
				Type:   action.Type,
				Label:  action.Label,
				Config: action.Config,
			})
		}
		detail.AvailableActions = actionInfos
	}

	// 获取当前状态信息
	if stateInfo, err := stateMachine.GetStateInfo(currentState); err == nil {
		detail.CurrentStateInfo = &api.StateInfo{
			ID:   int(stateInfo.ID),
			Name: stateInfo.Name,
			Type: stateInfo.Type,
		}
	}

	// 获取工作流信息
	workflowInfo := stateMachine.GetWorkflowInfo()
	if workflowInfo != nil {
		detail.WorkflowInfo = &api.WorkflowInfo{
			Name:         workflowInfo.Name,
			Slug:         workflowInfo.Slug,
			CurrentState: apply.State,
			StartState:   int(workflowInfo.StartState),
		}
	}

	// 获取回传数据（如果存在）
	postbackData, err := e.getPostbackData(c, apply)
	if err != nil {
		log.Error("获取回传数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
	} else {
		detail.PostbackData = postbackData
	}

	// 获取物料数据
	materialData, err := e.getMaterialData(c, apply)
	if err != nil {
		log.Error("获取物料数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
	} else {
		detail.MaterialData = materialData
	}

	// 获取审核历史（如果存在）
	// TODO: 实现审核历史获取逻辑

	return detail, nil
}

func (e endpointApplyService) CreateEndpointApply(c *gin.Context, req *api.CreatedEndpointApplyReq) error {
	// 判断是否是政策建店
	if req.PolicyID > 0 {
		//政策建店判断是否超过政策上限
		checkResult, err := e.CheckApplicable(c, req.PolicyID, 0)
		if err != nil {
			return err
		}
		if !checkResult {
			return errors.NewErr("终端申请数量已超出政策上限")
		}
	}
	// 经纬度处理
	blng, err := strconv.ParseFloat(req.Blng, 64)
	if err != nil {
		return errors.NewErr("Error parsing longitude")
	}

	blat, err := strconv.ParseFloat(req.Blat, 64)
	if err != nil {
		return errors.NewErr("Error parsing latitude")
	}
	lng, lat := coordtransform.BD09toGCJ02(blng, blat)
	// 装修实景图处理 - 转换为相对路径存储
	var pics string
	if req.Pics != nil && len(req.Pics) > 0 {
		picsRelative := make([]string, len(req.Pics))
		for i, pic := range req.Pics {
			picsRelative[i] = pic.GetRelativePath()
		}
		picsBytes, err := json.Marshal(picsRelative)
		if err != nil {
			return err
		}
		pics = string(picsBytes)
	}

	// 外部环境图处理 - 转换为相对路径存储
	var picsExternal string
	if req.PicsExternal != nil && len(req.PicsExternal) > 0 {
		picsExternalRelative := make([]string, len(req.PicsExternal))
		for i, pic := range req.PicsExternal {
			picsExternalRelative[i] = pic.GetRelativePath()
		}
		picsExternalBytes, err := json.Marshal(picsExternalRelative)
		if err != nil {
			return err
		}
		picsExternal = string(picsExternalBytes)
	}

	// 内部环境图处理 - 转换为相对路径存储
	var picsInternal string
	if req.PicsInternal != nil && len(req.PicsInternal) > 0 {
		picsInternalRelative := make([]string, len(req.PicsInternal))
		for i, pic := range req.PicsInternal {
			picsInternalRelative[i] = pic.GetRelativePath()
		}
		picsInternalBytes, err := json.Marshal(picsInternalRelative)
		if err != nil {
			return err
		}
		picsInternal = string(picsInternalBytes)
	}

	// 平面图处理 - 转换为相对路径存储
	var picsDesign string
	if req.PicsDesign != nil && len(req.PicsDesign) > 0 {
		picsDesignRelative := make([]string, len(req.PicsDesign))
		for i, pic := range req.PicsDesign {
			picsDesignRelative[i] = pic.GetRelativePath()
		}
		picsDesignBytes, err := json.Marshal(picsDesignRelative)
		if err != nil {
			return err
		}
		picsDesign = string(picsDesignBytes)
	}

	// 扩展字段处理
	var extend []byte
	if req.Extend != nil && len(req.Extend) > 0 {
		extend, err = json.Marshal(req.Extend)
		if err != nil {
			return err
		}
	}
	// 校验预计开业时间是否是时间格式
	var expectOpenTime time.Time
	if req.ExpectOpenTime != "" {
		expectOpenTime, err = time.Parse(time.DateOnly, req.ExpectOpenTime)
		if err != nil {
			return errors.NewErr("预计开业时间格式错误")
		}
	}

	// 根据政策获取状态机
	stateMachine, err := statemachine.CreateStateMachineForPolicy(req.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return err
	}

	// 获取工作流的初始状态
	workflowInfo := stateMachine.GetWorkflowInfo()
	var nextState int
	if workflowInfo != nil {
		// 从工作流获取下一状态提示
		nextStates, err := stateMachine.GetNextStates(workflowInfo.StartState)
		if err == nil && len(nextStates) > 0 {
			nextState = int(nextStates[0])
		}
	}

	data := model.EndpointApplication{
		TopAgency:       req.TopAgency,
		SecondAgency:    req.SecondAgency,
		Name:            req.Name,
		Type:            req.Type,
		Province:        req.Province,
		City:            req.City,
		District:        req.District,
		Address:         req.Address,
		Lng:             cast.ToString(lng),
		Lat:             cast.ToString(lat),
		ChannelLevel:    req.ChannelLevel,
		Blng:            req.Blng,
		Blat:            req.Blat,
		PolicyID:        req.PolicyID,
		Phone:           &req.Phone,
		Manager:         req.Manager,
		Investor:        req.Investor,
		InvestorPhone:   req.InvestorPhone,
		ExpectOpenTime:  types.DateOnly(expectOpenTime),
		Position:        req.Position,
		EndpointArea:    req.EndpointArea,
		Pics:            pics,
		PicsExternal:    picsExternal,
		PicsInternal:    picsInternal,
		PicsDesign:      picsDesign,
		Extend:          string(extend),
		State:           int(workflowInfo.StartState), // 使用工作流的初始状态
		NextState:       nextState,                    // 使用工作流计算的下一状态
		ApplicationYear: time.Now().Year(),
		CreatedAt:       time.Now(),
	}

	return e.repo.CreateEndpointApply(c, &data)
}

func (e endpointApplyService) UpdateEndpointApply(c *gin.Context, id int, req *api.CreatedEndpointApplyReq) error {
	data := make(map[string]any)
	//判断申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, id)
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}
	if apply.State != int(consts.ApplicationWaitingReview) {
		return errors.NewErr("终端申请已处理，不能再进行修改")
	}
	//判断政策是否存在
	if req.PolicyID > 0 && req.PolicyID != apply.PolicyID {
		//政策建店判断是否超过政策上限
		checkResult, err := e.CheckApplicable(c, req.PolicyID, id)
		if err != nil {
			return err
		}
		if !checkResult {
			return errors.NewErr("终端申请数量已超出政策上限")
		}
	}
	if req.Blat != apply.Blat || req.Blng != apply.Blng {
		// 经纬度处理
		blng, err := strconv.ParseFloat(req.Blng, 64)
		if err != nil {
			return errors.NewErr("Error parsing longitude")
		}

		blat, err := strconv.ParseFloat(req.Blat, 64)
		if err != nil {
			return errors.NewErr("Error parsing latitude")
		}
		lng, lat := coordtransform.BD09toGCJ02(blng, blat)
		data["lng"] = cast.ToString(lng)
		data["lat"] = cast.ToString(lat)
		data["blng"] = req.Blng
		data["blat"] = req.Blat
	}
	// 装修实景图处理 - 转换为相对路径存储
	if req.Pics != nil && len(req.Pics) > 0 {
		picsRelative := make([]string, len(req.Pics))
		for i, pic := range req.Pics {
			picsRelative[i] = pic.GetRelativePath()
		}
		picsBytes, err := json.Marshal(picsRelative)
		if err != nil {
			return err
		}
		data["pics"] = string(picsBytes)
	}

	// 外部环境图处理 - 转换为相对路径存储
	if req.PicsExternal != nil && len(req.PicsExternal) > 0 {
		picsExternalRelative := make([]string, len(req.PicsExternal))
		for i, pic := range req.PicsExternal {
			picsExternalRelative[i] = pic.GetRelativePath()
		}
		picsExternalBytes, err := json.Marshal(picsExternalRelative)
		if err != nil {
			return err
		}
		data["pics_external"] = string(picsExternalBytes)
	}

	// 内部环境图处理 - 转换为相对路径存储
	if req.PicsInternal != nil && len(req.PicsInternal) > 0 {
		picsInternalRelative := make([]string, len(req.PicsInternal))
		for i, pic := range req.PicsInternal {
			picsInternalRelative[i] = pic.GetRelativePath()
		}
		picsInternalBytes, err := json.Marshal(picsInternalRelative)
		if err != nil {
			return err
		}
		data["pics_internal"] = string(picsInternalBytes)
	}

	// 平面图处理 - 转换为相对路径存储
	if req.PicsDesign != nil && len(req.PicsDesign) > 0 {
		picsDesignRelative := make([]string, len(req.PicsDesign))
		for i, pic := range req.PicsDesign {
			picsDesignRelative[i] = pic.GetRelativePath()
		}
		picsDesignBytes, err := json.Marshal(picsDesignRelative)
		if err != nil {
			return err
		}
		data["pics_design"] = string(picsDesignBytes)
	}

	// 扩展字段处理
	if req.Extend != nil && len(req.Extend) > 0 {
		extend, err := json.Marshal(req.Extend)
		if err != nil {
			return err
		}
		data["extend"] = string(extend)
	}

	// 校验预计开业时间是否是时间格式
	var expectOpenTime time.Time
	if req.ExpectOpenTime != "" {
		expectOpenTime, err = time.Parse(time.DateOnly, req.ExpectOpenTime)
		if err != nil {
			return errors.NewErr("预计开业时间格式错误")
		}
		data["expect_open_time"] = expectOpenTime.Format(time.DateOnly)
	}
	//处理更新数据
	data["policy_id"] = req.PolicyID
	data["name"] = req.Name
	data["type"] = req.Type
	data["province"] = req.Province
	data["city"] = req.City
	data["district"] = req.District
	data["address"] = req.Address
	data["channel_level"] = req.ChannelLevel
	data["phone"] = req.Phone
	data["manager"] = req.Manager
	data["investor"] = req.Investor
	data["investor_phone"] = req.InvestorPhone
	data["position"] = req.Position
	data["endpoint_area"] = req.EndpointArea
	data["state"] = req.State
	data["application_year"] = time.Now().Year()
	data["next_state"] = int(consts.ApplicationApproved) // 下一个状态，管理审核
	return e.repo.UpdateEndpointApply(c, id, data)
}

func (e endpointApplyService) DownloadWriteOffTable(c *gin.Context, id int) error {
	// 查询相关的政策
	apply, err := e.repo.GetEndpointApplyByID(c, id)
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("申请不存在")
	}
	// 查询相关的政策
	policy, err := e.policyRepo.GetEndpointPolicyByID(c, apply.PolicyID)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("政策不存在")
	}
	if policy.WriteOffTable == "" {
		return errors.NewErr("政策不存在批复表模板")
	}
	var agencyName string
	var ok bool
	agencyMap, err := e.endpointRepo.GetAgencies(c, []int{int(apply.TopAgency), int(apply.SecondAgency)})
	if agencyName, ok = agencyMap[apply.TopAgency]; !ok {
		return errors.NewErr("渠道不存在")
	}
	if apply.SecondAgency == 0 {
		agencyName += "直营"
	} else {
		if secondAgencyName, ok := agencyMap[apply.SecondAgency]; ok {
			agencyName += secondAgencyName
		}
	}

	// 下载模板
	objectKey := utils.DeletePrefix(string(policy.WriteOffTable))
	docData, err := oss.Download(objectKey)
	if err != nil {
		log.Error("模板下载失败" + err.Error())
		return err
	}
	doc, err := document.Read(bytes.NewReader(docData), int64(len(docData)))
	if err != nil {
		return err
	}
	var (
		saleAmount float64
	)

	//查询建店信息
	endpoint, err := e.endpointRepo.GetEndpointByID(c, apply.AddToEndpointId)
	if err != nil {
		return err
	}
	if endpoint == nil {
		return errors.NewErr("终端不存在")
	}

	table := doc.Tables()[0]

	// 初始化销售金额
	if apply.AddToEndpointId > 0 {
		salesAmountInt64, _, _ := e.SalesAmount(c, int(apply.AddToEndpointId), 1, apply.CreatedAt.Unix())
		saleAmount = float64(salesAmountInt64)
	}

	fillTableCell := func(row, col int, text string) {
		cell := table.Rows()[row].Cells()[col]
		para := cell.Paragraphs()
		run := para[0].AddRun()
		run.Properties().SetFontFamily("黑体")
		run.AddText(text)
	}

	// 统一填充字段
	fillTableCell(0, 0, apply.Name)
	fillTableCell(0, 1, endpoint.Code)
	fillTableCell(1, 0, endpoint.Address)
	fillTableCell(1, 1, apply.ExpectOpenTime.String())
	fillTableCell(2, 0, apply.Investor)
	fillTableCell(2, 1, apply.InvestorPhone)
	fillTableCell(3, 0, apply.EndpointArea)
	fillTableCell(3, 1, cast.ToString(saleAmount))
	fillTableCell(4, 0, apply.Pay)
	fillTableCell(4, 1, agencyName)

	// 添加物料支持明细（新版不做类型过滤）
	materialRow := 6
	cell := table.Rows()[materialRow].Cells()[0]
	para := cell.Paragraphs()
	run := para[0].AddRun()
	run.Properties().SetFontFamily("黑体")
	run.AddText("物料支持明细：")
	run.AddBreak()

	//查询物料支持明细
	materialData, err := e.getMaterialData(c, apply)
	if err != nil {
		log.Error("获取物料数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
		// 如果获取失败，继续处理但不添加物料信息
	} else {
		// 根据不同格式填充物料信息
		if materialData.IsRawFormat {
			// 原始字符串格式，直接添加原始文本
			run.AddText(materialData.RawDetail)
			run.AddBreak()
		} else if len(materialData.MaterialList) > 0 {
			// 结构化格式，按照 GoFrame 版本的格式输出
			for _, item := range materialData.MaterialList {
				run.AddText(fmt.Sprintf("%s * %d", item.Name, item.Quantity))
				run.AddBreak()
			}
		}
	}
	var bs bytes.Buffer
	if err := doc.Save(&bs); err != nil {
		return err
	}
	pdf, err := utils.ConvertDocxToPDF(bs.Bytes())
	if err != nil {
		return err
	}
	// 设置响应头，告知浏览器这是一个 PDF 文件
	fileName := fmt.Sprintf("终端批复表_%s.pdf", time.Now().Format("20060102_150405"))
	c.Header("Content-Type", "application/pdf")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", fileName))

	// 写入响应体
	_, err = c.Writer.Write(pdf)
	if err != nil {
		return err
	}
	return nil
}

func (e endpointApplyService) LatestEndpointImage(c *gin.Context, id int) (*api.LatestEndpointImageResp, error) {
	imageInfo, err := e.endpointImageDao.GetLatest(c, id)
	if err != nil {
		return nil, err
	}
	var resp api.LatestEndpointImageResp
	if imageInfo == nil {
		return &resp, nil
	}
	// 实现 SQL 逻辑：IF(status = 'approved', audit_time, null)
	if imageInfo.Status == "approved" {
		resp.AutoTime = imageInfo.AuditTime
	}
	// 实现 SQL 逻辑：IF(manual_status = 'approved', manual_audit_time, null)
	if imageInfo.ManualStatus == "approved" {
		resp.ManualTime = imageInfo.ManualAuditTime
	}
	return &resp, nil
}

func (e endpointApplyService) SalesAmount(c *gin.Context, id int, years int, endAt int64) (int64, float64, error) {
	end := time.Unix(endAt, 0)
	status := 1
	endTime := end.AddDate(0, -1, 0).Format(time.DateTime)
	// 计算起始时间为上一个月的当前时间
	startTime := end.AddDate(-years, 0, 0).Format(time.DateTime)
	var params warrantyDao.StatParams
	params.EndpointID = uint(id)
	params.StartTime = startTime
	params.EndTime = endTime
	params.Status = &status
	params.Realsale = &status
	amount, err := e.warrantyRepo.StatCountAndAmount(c, &params)
	if err != nil {
		return 0, 0, err
	}
	return amount.Count, amount.Amount, nil
}

func (e endpointApplyService) GetMaterials(c *gin.Context, name string, categories []int, page, pageSize int) ([]*api.EndpointMaterialSupport, error) {
	var materials []*api.EndpointMaterialSupport
	var param dao.GetMaterialListParam
	param.Name = name
	param.PageNum = page
	param.PageSize = pageSize
	param.Categories = categories
	param.IsPutAway = 1
	data, _ := e.materialRepo.GetMaterialList(c, &param)
	for _, material := range data {
		materials = append(materials, &api.EndpointMaterialSupport{
			ID:               cast.ToUint(material.Id),
			Name:             material.Name,
			Pic:              material.Pic,
			Price:            cast.ToFloat64(material.Price),
			ProductionNumber: material.ProductionNumber,
			Thumbnail:        material.Thumbnail,
		})
	}
	return materials, nil
}

// GetWorkflowSteps 获取工作流步骤
func (e endpointApplyService) GetWorkflowSteps(c *gin.Context, id uint) ([]*WorkflowStep, error) {
	// 获取申请信息
	apply, err := e.repo.GetEndpointApplyByID(c, int(id))
	if err != nil {
		return nil, err
	}
	if apply == nil {
		return nil, errors.NewErr("申请不存在")
	}

	// 获取工作流配置
	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return nil, err
	}

	workflow := stateMachine.GetWorkflowInfo()
	if workflow == nil {
		return nil, errors.NewErr("工作流配置不存在")
	}

	// 根据当前状态构建工作流步骤
	return e.buildWorkflowSteps(apply.State, workflow), nil
}

func (e endpointApplyService) TransitionState(c *gin.Context, req *api.StateTransitionReq) error {
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("endpoint application not found")
	}

	policy, err := e.policyRepo.GetEndpointPolicyByID(c, apply.PolicyID)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("policy not found")
	}

	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return err
	}

	stateReq := &statemachine.StateTransitionRequest{
		ApplicationID: req.ID,
		CurrentState:  consts.EndpointApplicationState(apply.State),
		Action:        req.Action,
		Data:          req.Data,
	}

	// 兼容：允许从 data 中传目标状态（若 action 为空，将在引擎中反推）
	if req.Data != nil {
		if v, ok := req.Data["target_state"]; ok {
			if ts, ok2 := v.(int); ok2 {
				stateReq.TargetState = consts.EndpointApplicationState(ts)
			}
		}
		if stateReq.TargetState == 0 {
			if v, ok := req.Data["status"]; ok {
				if ts, ok2 := v.(int); ok2 {
					stateReq.TargetState = consts.EndpointApplicationState(ts)
				}
			}
		}
	}

	stateReq.Data["application"] = apply
	stateReq.Data["policy"] = policy

	return e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		stateReq.Data["txRepo"] = txRepo
		stateReq.Data["service"] = e
		stateReq.Data["configRepo"] = e.configRepo

		// 执行状态机转换（状态机内部会调用相应的处理器）
		if err := stateMachine.ExecuteTransition(c, stateReq); err != nil {
			return err
		}

		// 记录状态变换日志
		return e.createStateLog(c, txRepo, stateReq)
	})
}

func (e endpointApplyService) createStateLog(c *gin.Context, txRepo applicationDao.EndpointApplyDao, req *statemachine.StateTransitionRequest) error {
	status := &model.EndpointApplicationStatus{
		ApplicationID: int(req.ApplicationID),
		BeforeState:   int(req.CurrentState),
		AfterState:    int(req.TargetState),
		Extend:        utils.JsonMarshals(req.Data),
		HandlerID:     int(c.GetUint("uid")),
		Remark:        fmt.Sprintf("Action: %s", req.Action),
	}

	return txRepo.CreateEndpointApplicationStatus(c, status)
}

// AuditApply 审核申请 - 通过状态机处理
func (e endpointApplyService) AuditApply(c *gin.Context, req *api.AuditApplyReq) error {
	var action string
	if req.State == int(consts.ApplicationApproved) {
		action = "approve"
	} else if req.State == int(consts.ApplicationRejected) {
		action = "reject"
	} else {
		return fmt.Errorf("invalid audit state: %d", req.State)
	}

	return e.TransitionState(c, &api.StateTransitionReq{
		ID:     req.ID,
		Action: action,
		Data: map[string]interface{}{
			"extend":       req.Extend,
			"audit_advice": req.AuditAdvice,
			"service":      e,
		},
	})
}

// MaterialSupport 物料支持 - 通过状态机处理
func (e endpointApplyService) MaterialSupport(c *gin.Context, id uint, req []*api.EndpointMaterialSupport) error {
	return e.TransitionState(c, &api.StateTransitionReq{
		ID:     id,
		Action: "submit_material",
		Data: map[string]interface{}{
			"material_list": req,
		},
	})
}

// PostbackEndpointApply 建店回传 - 通过状态机处理
func (e endpointApplyService) PostbackEndpointApply(c *gin.Context, req *api.PostbackEndpointApplyReq) error {
	return e.TransitionState(c, &api.StateTransitionReq{
		ID:     req.ID,
		Action: "postback",
		Data: map[string]interface{}{
			"write_off_table":      req.WriteOffTable,
			"lease_contract":       req.LeaseContract,
			"annual_rent":          req.AnnualRent,
			"design_renderings":    req.DesignRenderings,
			"renovation_photos":    req.RenovationPhotos,
			"renovation_videos":    req.RenovationVideos,
			"diploma":              req.Diploma,
			"endpoint_group_photo": req.EndpointGroupPhoto,
			"extend":               req.Extend,
		},
	})
}

// WriteOff 核销初审 - 通过状态机处理
func (e endpointApplyService) WriteOff(c *gin.Context, req *api.WriteOffReq) error {
	action := "approve"
	if req.State == int(consts.ApplicationInitialWriteOffRejected) {
		action = "reject"
	}

	// 构建状态转换数据
	data := map[string]interface{}{
		"pay":                req.Pay,
		"write_off_advice":   req.WriteOffAdvice,
		"bag_support_amount": req.BagSupportAmount,
		"extend":             req.Extend,
	}

	return e.TransitionState(c, &api.StateTransitionReq{
		ID:     req.ID,
		Action: action,
		Data:   data,
	})
}

// ChannelConfirmation 渠道确认 - 通过状态机处理
func (e endpointApplyService) ChannelConfirmation(c *gin.Context, req *api.ChannelAuditReq) error {
	action := "approve"
	if req.State == int(consts.ApplicationChannelConfirmationRejected) {
		action = "reject"
	}

	// 构建状态转换数据
	data := map[string]interface{}{
		"extend":         req.Extend,
		"channel_advice": req.ChannelAdvice,
	}

	// 添加渠道确认相关字段
	if req.RealOpenTime != 0 {
		data["real_open_time"] = req.RealOpenTime
	}
	if len(req.ChannelPhotos) > 0 {
		data["channel_photos"] = req.ChannelPhotos
	}

	return e.TransitionState(c, &api.StateTransitionReq{
		ID:     req.ID,
		Action: action,
		Data:   data,
	})
}

// RecordConfirmation 入账确认
func (e endpointApplyService) RecordConfirmation(c *gin.Context, req *api.AuditApplyReq) error {
	// 获取申请信息
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}

	// 获取政策信息
	policy, err := e.policyRepo.GetEndpointPolicyByID(c, apply.PolicyID)
	if err != nil {
		return err
	}
	if policy == nil {
		return errors.NewErr("政策不存在")
	}

	// 创建状态机
	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return err
	}

	// 构建状态转换请求
	stateReq := &statemachine.StateTransitionRequest{
		ApplicationID: req.ID,
		CurrentState:  consts.EndpointApplicationState(apply.State),
		TargetState:   consts.EndpointApplicationState(req.State),
		Action:        "confirm_record",
		Data:          make(map[string]interface{}),
	}

	// 设置请求数据
	stateReq.Data["application"] = apply
	stateReq.Data["policy"] = policy
	stateReq.Data["extend"] = req.Extend

	// 使用事务执行状态转换
	return e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		stateReq.Data["txRepo"] = txRepo
		stateReq.Data["service"] = e
		stateReq.Data["configRepo"] = e.configRepo

		// 执行状态转换
		if err := stateMachine.ExecuteTransition(c, stateReq); err != nil {
			return err
		}

		return nil
	})
}

// Terminate 建店终止 - 直接终止，不受工作流状态限制
func (e endpointApplyService) Terminate(c *gin.Context, req *api.TerminateReq) error {
	// 验证申请是否存在
	apply, err := e.repo.GetEndpointApplyByID(c, int(req.ID))
	if err != nil {
		return err
	}
	if apply == nil {
		return errors.NewErr("终端申请不存在")
	}

	// 验证终止原因
	if req.TerminateReason == "" {
		return errors.NewErr("终止原因不能为空")
	}

	// 如果已经是终止状态，直接返回
	if apply.State == int(consts.ApplicationCancelled) {
		return errors.NewErr("申请已处于终止状态")
	}

	// 记录原始状态
	beforeState := apply.State

	// 使用事务直接更新状态，绕过工作流限制
	return e.repo.WithTransaction(c, func(txRepo applicationDao.EndpointApplyDao) error {
		// 直接更新申请状态为终止
		updateData := map[string]any{
			"state":      int(consts.ApplicationCancelled),
			"next_state": 0, // 终止状态没有下一状态
		}

		// 更新申请记录
		if err := txRepo.UpdateEndpointApply(c, int(req.ID), updateData); err != nil {
			return err
		}

		// 创建状态转换记录
		statusRecord := &model.EndpointApplicationStatus{
			ApplicationID: int(req.ID),
			BeforeState:   beforeState,
			AfterState:    int(consts.ApplicationCancelled),
			HandlerID:     int(c.GetUint("uid")),
			Remark:        fmt.Sprintf("建店终止：%s", req.TerminateReason),
			Extend:        utils.JsonMarshals(req.Extend),
		}

		return txRepo.CreateEndpointApplicationStatus(c, statusRecord)
	})
}

// EndpointCreated 终端创建后的钩子方法
func (e endpointApplyService) EndpointCreated(c *gin.Context, endpointInfo *model.Endpoint) {
	//桌椅权限处理
	err := e.endpointRepo.GrantH5PermissionToEndpoint(c, endpointInfo)
	if err != nil {
		log.Error(" GrantH5PermissionToEndpoint err:" + err.Error())
	}
	//课程处理
	err = e.subjectRepo.BindToEndpoint(c, endpointInfo)
	if err != nil {
		log.Error(" GrantCourseToEndpoint err:" + err.Error())
	}
}

// GetAvailableActions 获取当前状态的可用操作
func (e endpointApplyService) GetAvailableActions(c *gin.Context, id uint) ([]statemachine.Action, error) {
	// 获取申请信息
	apply, err := e.repo.GetEndpointApplyByID(c, int(id))
	if err != nil {
		return nil, err
	}
	if apply == nil {
		return nil, errors.NewErr("endpoint application not found")
	}

	// 创建状态机
	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return nil, err
	}

	// 获取当前状态的可用操作
	currentState := consts.EndpointApplicationState(apply.State)
	return stateMachine.GetAvailableActions(currentState)
}

// GetNextStates 获取当前状态的可能下一状态
func (e endpointApplyService) GetNextStates(c *gin.Context, id uint) ([]api.StateInfo, error) {
	// 获取申请信息
	apply, err := e.repo.GetEndpointApplyByID(c, int(id))
	if err != nil {
		return nil, err
	}
	if apply == nil {
		return nil, errors.NewErr("endpoint application not found")
	}

	// 创建状态机
	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return nil, err
	}

	// 获取当前状态的下一状态
	currentState := consts.EndpointApplicationState(apply.State)
	nextStates, err := stateMachine.GetNextStates(currentState)
	if err != nil {
		return nil, err
	}

	// 转换为StateInfo格式
	var stateInfos []api.StateInfo
	for _, state := range nextStates {
		if stateNode, err := stateMachine.GetStateInfo(state); err == nil {
			stateInfos = append(stateInfos, api.StateInfo{
				ID:   int(stateNode.ID),
				Name: stateNode.Name,
				Type: stateNode.Type,
			})
		}
	}

	return stateInfos, nil
}

func (e endpointApplyService) CheckApplicable(c *gin.Context, policyID int, id int) (bool, error) {
	policyInfo, err := e.policyRepo.GetEndpointPolicyByID(c, policyID)
	if err != nil {
		return false, err
	}
	if policyInfo == nil {
		return false, errors.NewErr("政策不存在")
	}
	if policyInfo.Enabled == 0 {
		return false, errors.NewErr("政策已禁用")
	}
	if policyInfo.StartDate.After(time.Now()) {
		return false, errors.NewErr("政策未开始")
	}
	if policyInfo.EndDate.Before(time.Now()) {
		return false, errors.NewErr("政策已结束")
	}
	// 计算申请是否已经超出上限
	if policyInfo.Maximum > 0 {
		// 计算当前政策已申请数量
		applyCount, err := e.repo.CountEndpointApplyByPolicyID(c, policyID, id)
		if err != nil {
			return false, err
		}
		if applyCount >= policyInfo.Maximum {
			return false, errors.NewErr("终端申请数量已超出政策上限")
		}
	}
	return true, nil
}

// getMaterialData 获取物料数据，兼容新旧版本
func (e endpointApplyService) getMaterialData(c *gin.Context, apply *model.EndpointApplication) (*api.MaterialData, error) {
	materialData := &api.MaterialData{
		MaterialList: []api.MaterialItem{},
		TotalAmount:  0,
	}

	// 1. 优先从新表 endpoint_material_support 获取数据
	newMaterials, err := e.repo.GetEndpointMaterialSupportById(c, apply.ID)
	if err != nil {
		log.Error("获取新版物料数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
	} else if len(newMaterials) > 0 {
		// 有新版数据，使用新版数据
		for _, material := range newMaterials {
			amount := float64(material.Num) * material.Price
			materialData.MaterialList = append(materialData.MaterialList, api.MaterialItem{
				ID:       material.ID,
				Name:     material.Name,
				Quantity: material.Num,
				Price:    material.Price,
				Amount:   amount,
			})
			materialData.TotalAmount += amount
		}
		return materialData, nil
	}

	// 2. 如果新表没有数据，尝试从 extend 字段获取旧版数据
	if apply.Extend == "" {
		return materialData, nil
	}

	var extendData map[string]interface{}
	if err := json.Unmarshal([]byte(apply.Extend), &extendData); err != nil {
		log.Error("解析extend字段失败", zap.Error(err), zap.Int("application_id", apply.ID))
		return materialData, nil
	}

	// 3. 处理旧版物料数据的两种格式
	if err := e.parseLegacyMaterialData(extendData, materialData); err != nil {
		log.Error("解析旧版物料数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
	}

	return materialData, nil
}

// parseLegacyMaterialData 解析旧版物料数据
func (e endpointApplyService) parseLegacyMaterialData(extendData map[string]interface{}, materialData *api.MaterialData) error {
	// 优先检查是否有 material_support_detail_map，有的话就解析成跟新版一样的格式
	if detailMap, exists := extendData["material_support_detail_map"]; exists && detailMap != nil {
		if detailAmount, exists := extendData["material_support_detail_amount"]; exists {
			return e.parseMapMaterialData(detailMap, detailAmount, materialData)
		}
	}

	// 如果没有 material_support_detail_map，就直接返回 material_support_detail 字符串
	if detailStr, exists := extendData["material_support_detail"]; exists && detailStr != nil {
		if detailString, ok := detailStr.(string); ok && detailString != "" {
			// 直接返回原始字符串格式
			materialData.RawDetail = detailString
			materialData.IsRawFormat = true
			materialData.MaterialList = []api.MaterialItem{} // 空列表
			materialData.TotalAmount = 0
			return nil
		}
	}

	return nil
}

// parseMapMaterialData 解析 map 格式的物料数据
func (e endpointApplyService) parseMapMaterialData(detailMap interface{}, detailAmount interface{}, materialData *api.MaterialData) error {
	// 解析总金额
	if amount, ok := detailAmount.(float64); ok {
		materialData.TotalAmount = amount
	} else if amountStr, ok := detailAmount.(string); ok {
		if amount, err := strconv.ParseFloat(amountStr, 64); err == nil {
			materialData.TotalAmount = amount
		}
	}

	// 解析物料详情 map
	if materialMap, ok := detailMap.(map[string]interface{}); ok {
		index := 1
		for name, details := range materialMap {
			item := api.MaterialItem{
				ID:   uint(index),
				Name: name,
			}

			if detailsMap, ok := details.(map[string]interface{}); ok {
				// 解析数量
				if quantity, ok := detailsMap["quantity"]; ok {
					if q, ok := quantity.(float64); ok {
						item.Quantity = int(q)
					}
				}

				// 解析价格
				if price, ok := detailsMap["price"]; ok {
					if p, ok := price.(float64); ok {
						item.Price = p
					}
				}
			}

			item.Amount = float64(item.Quantity) * item.Price
			materialData.MaterialList = append(materialData.MaterialList, item)
			index++
		}
	}

	return nil
}

// getPostbackData 获取回传数据，兼容新旧版本
func (e endpointApplyService) getPostbackData(c *gin.Context, apply *model.EndpointApplication) (*api.PostbackData, error) {
	postbackData := &api.PostbackData{}

	// 1. 优先从新表 endpoint_application_postback 获取数据
	newPostback, err := e.repo.GetEndpointApplicationPostbackById(c, apply.ID)
	if err != nil {
		log.Error("获取新版回传数据失败", zap.Error(err), zap.Int("application_id", apply.ID))
	} else if newPostback != nil {
		// 有新版数据，使用新版数据
		postbackData.WriteOffTable = types.OssPath(newPostback.WriteOffTable)
		postbackData.LeaseContract = types.OssPath(newPostback.LeaseContract)
		postbackData.AnnualRent = newPostback.AnnualRent

		// 使用通用方法解析 JSON 字符串为数组
		types.UnmarshalJSONField(newPostback.DesignRenderings, &postbackData.DesignRenderings)
		types.UnmarshalJSONField(newPostback.RenovationPhotos, &postbackData.RenovationPhotos)
		types.UnmarshalJSONField(newPostback.RenovationVideos, &postbackData.RenovationVideos)
		types.UnmarshalJSONField(newPostback.Diploma, &postbackData.Diploma)
		types.UnmarshalJSONField(newPostback.EndpointGroupPhoto, &postbackData.EndpointGroupPhoto)

		postbackData.Extend = newPostback.Extend
		return postbackData, nil
	}

	// 2. 如果新表没有数据，尝试从主表的 extend 字段获取旧版数据（兼容性处理）
	if apply.Extend == "" {
		return postbackData, nil
	}

	var extendData map[string]interface{}
	if err := json.Unmarshal([]byte(apply.Extend), &extendData); err != nil {
		log.Error("解析extend字段失败", zap.Error(err), zap.Int("application_id", apply.ID))
		return postbackData, nil
	}

	// 3. 处理旧版回传数据
	e.parseLegacyPostbackData(extendData, postbackData)

	// 4. 如果从旧数据中获取到了回传数据，记录日志（用于后续迁移）
	if e.hasPostbackData(postbackData) {
		log.Info("从extend字段获取到回传数据，建议迁移到新表",
			zap.Int("application_id", apply.ID),
			zap.String("action", "suggest_migration"))
	}

	return postbackData, nil
}

// hasPostbackData 检查是否有回传数据
func (e endpointApplyService) hasPostbackData(postbackData *api.PostbackData) bool {
	return postbackData.WriteOffTable != "" ||
		postbackData.LeaseContract != "" ||
		postbackData.AnnualRent > 0 ||
		len(postbackData.DesignRenderings) > 0 ||
		len(postbackData.RenovationPhotos) > 0 ||
		len(postbackData.RenovationVideos) > 0 ||
		len(postbackData.Diploma) > 0 ||
		len(postbackData.EndpointGroupPhoto) > 0
}

// parseLegacyPostbackData 解析旧版回传数据
func (e endpointApplyService) parseLegacyPostbackData(extendData map[string]interface{}, postbackData *api.PostbackData) {

	// 解析确认时间
	if confirmDate, exists := extendData["confirm_date"]; exists {
		if confirmDateStr, ok := confirmDate.(string); ok && confirmDateStr != "" {
			if t, err := time.Parse(time.DateTime, confirmDateStr); err == nil {
				postbackData.ConfirmDate = types.CustomTime(t)
			}
		}
	}

	// 解析装修实景视频
	if renovationVideos, exists := extendData["renovation_videos"]; exists {
		if videosStr, ok := renovationVideos.(string); ok {
			types.UnmarshalJSONField(videosStr, &postbackData.RenovationVideos)
		}
	}

}

// GetWorkflowDetailWithSteps 获取工作流详情和步骤（整合接口）
func (e endpointApplyService) GetWorkflowDetailWithSteps(c *gin.Context, id uint, agencyID uint, agencyLevel int) (*api.WorkflowDetailWithStepsResp, error) {
	// 获取申请信息
	apply, err := e.repo.GetEndpointApplyByID(c, int(id))
	if err != nil {
		return nil, err
	}
	if apply == nil {
		return nil, errors.NewErr("申请不存在")
	}

	// 验证权限：确保申请属于当前用户的代理商
	if agencyLevel == 1 {
		// 一级代理商，检查top_agency
		if apply.TopAgency != agencyID {
			return nil, errors.NewErr("无权限查看此申请")
		}
	} else {
		// 二级代理商，检查second_agency
		if apply.SecondAgency != agencyID {
			return nil, errors.NewErr("无权限查看此申请")
		}
	}

	// 获取申请详情
	detail, err := e.GetEndpointApplyDetail(c, id)
	if err != nil {
		return nil, err
	}

	// 获取状态机
	stateMachine, err := statemachine.CreateStateMachineForPolicy(apply.PolicyID, e.workflowDao, e, e)
	if err != nil {
		return nil, err
	}

	workflow := stateMachine.GetWorkflowInfo()
	if workflow == nil {
		return nil, errors.NewErr("工作流配置不存在")
	}

	// 构建带表单字段的工作流步骤
	workflowSteps, err := e.buildWorkflowStepsWithForm(apply.State, workflow, apply)
	if err != nil {
		return nil, err
	}

	// 获取当前可执行的操作
	currentState := consts.EndpointApplicationState(apply.State)
	availableActions, err := stateMachine.GetAvailableActions(currentState)
	if err != nil {
		return nil, err
	}

	var actionInfos []api.ActionInfo
	for _, action := range availableActions {
		actionInfos = append(actionInfos, api.ActionInfo{
			Type:   action.Type,
			Label:  action.Label,
			Config: action.Config,
		})
	}

	// 构建工作流基本信息
	workflowInfo := &api.WorkflowBasicInfo{
		Name:       workflow.Name,
		Slug:       workflow.Slug,
		StartState: int(workflow.StartState),
		TotalSteps: len(workflowSteps),
	}

	return &api.WorkflowDetailWithStepsResp{
		ApplicationDetail: detail,
		WorkflowSteps:     workflowSteps,
		AvailableActions:  actionInfos,
		WorkflowInfo:      workflowInfo,
	}, nil
}

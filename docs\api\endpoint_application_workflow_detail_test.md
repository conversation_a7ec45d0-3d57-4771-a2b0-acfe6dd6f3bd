# 工作流详情整合接口测试指南

## 测试准备

### 1. 确保有测试数据
需要在数据库中有一个终端申请记录，包含：
- 基本申请信息
- 关联的政策ID
- 当前状态信息
- 扩展字段数据

### 2. 获取测试用户信息
需要一个有效的代理商用户，能够访问申请记录。

## 测试步骤

### 1. 基本接口测试

```bash
# 替换 {id} 为实际的申请ID
# 替换 {token} 为有效的用户token
curl -X GET "http://localhost:8080/agency/endpoint-application/{id}/workflow-detail" \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json"
```

### 2. 预期返回结构验证

检查返回的JSON结构是否包含以下主要字段：
- `application_detail`: 申请详情
- `workflow_steps`: 工作流步骤数组
- `available_actions`: 可用操作数组
- `workflow_info`: 工作流基本信息

### 3. 工作流步骤验证

每个步骤应该包含：
- `state`: 状态ID
- `name`: 状态名称
- `type`: 状态类型
- `order`: 步骤顺序
- `status`: 步骤状态
- `is_reached`: 是否已到达
- `form_fields`: 表单字段配置
- `form_data`: 已填写数据
- `actions`: 可执行操作

### 4. 表单字段配置验证

根据不同的状态类型，验证表单字段是否正确：

#### 审核状态 (audit)
- `audit_remark`: 审核备注字段

#### 物料状态 (material)
- `material_list`: 物料清单字段
- `need_material`: 是否需要物料支持字段

#### 核销状态 (write_off)
- `write_off_table`: 核销表字段
- `lease_contract`: 租赁合同字段
- `annual_rent`: 年租金字段

#### 物料支持完成状态 (material_supported)
- `design_renderings`: 设计效果图字段
- `renovation_photos`: 装修实景图字段
- `renovation_videos`: 装修实景视频字段

### 5. 权限测试

#### 测试用例1: 正常访问
使用有权限的代理商用户访问自己的申请，应该返回完整数据。

#### 测试用例2: 无权限访问
使用其他代理商用户访问不属于自己的申请，应该返回权限错误。

#### 测试用例3: 无效ID
使用不存在的申请ID，应该返回"申请不存在"错误。

## 测试检查点

### 1. 数据完整性
- [ ] 申请详情数据完整
- [ ] 工作流步骤数量正确
- [ ] 步骤顺序正确
- [ ] 状态判断准确

### 2. 表单字段配置
- [ ] 字段类型正确
- [ ] 必填标识正确
- [ ] 验证规则合理
- [ ] 选项配置完整

### 3. 状态逻辑
- [ ] `is_reached` 判断正确
- [ ] `status` 状态准确
- [ ] 当前步骤识别正确
- [ ] 可执行操作正确

### 4. 性能测试
- [ ] 响应时间合理（< 500ms）
- [ ] 内存使用正常
- [ ] 数据库查询优化

## 常见问题排查

### 1. 返回空数据
- 检查申请ID是否存在
- 检查用户权限
- 检查工作流配置

### 2. 表单字段缺失
- 检查状态类型映射
- 检查表单字段配置逻辑
- 检查状态机配置

### 3. 状态判断错误
- 检查 `isStepCompleted` 逻辑
- 检查 `isStepRejected` 逻辑
- 检查当前状态值

### 4. 权限错误
- 检查用户代理商信息
- 检查申请归属关系
- 检查代理商级别判断

## 示例测试脚本

```javascript
// 使用 JavaScript 进行接口测试
async function testWorkflowDetail(applicationId, token) {
  try {
    const response = await fetch(`/agency/endpoint-application/${applicationId}/workflow-detail`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const data = await response.json();
    
    // 基本结构检查
    console.assert(data.ok === 1, '接口调用失败');
    console.assert(data.data.application_detail, '申请详情缺失');
    console.assert(Array.isArray(data.data.workflow_steps), '工作流步骤不是数组');
    console.assert(Array.isArray(data.data.available_actions), '可用操作不是数组');
    console.assert(data.data.workflow_info, '工作流信息缺失');
    
    // 步骤检查
    const steps = data.data.workflow_steps;
    console.assert(steps.length > 0, '工作流步骤为空');
    
    steps.forEach((step, index) => {
      console.assert(step.order === index + 1, `步骤${index}顺序错误`);
      console.assert(typeof step.is_reached === 'boolean', `步骤${index}到达状态类型错误`);
      console.assert(Array.isArray(step.form_fields), `步骤${index}表单字段不是数组`);
      console.assert(typeof step.form_data === 'object', `步骤${index}表单数据类型错误`);
    });
    
    console.log('所有测试通过！');
    return data;
  } catch (error) {
    console.error('测试失败:', error);
    throw error;
  }
}
```

## 测试报告模板

```markdown
# 工作流详情接口测试报告

## 测试环境
- 服务器: [环境信息]
- 数据库: [数据库信息]
- 测试时间: [测试时间]

## 测试结果
- [ ] 基本功能正常
- [ ] 权限控制正确
- [ ] 数据完整性良好
- [ ] 性能表现良好

## 发现问题
1. [问题描述]
2. [问题描述]

## 改进建议
1. [建议内容]
2. [建议内容]
```

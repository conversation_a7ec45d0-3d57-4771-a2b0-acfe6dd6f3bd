# 终端申请工作流详情整合接口

## 接口概述

这是一个整合接口，将原有的 `GetEndpointApplyDetail` 和 `GetWorkflowSteps` 接口合并，提供完整的工作流详情信息，包括：
- 申请的详细信息
- 工作流的所有步骤状态
- 每个步骤的表单字段配置
- 每个步骤已填写的数据
- 当前可执行的操作

## 接口信息

- **请求URL**: `/agency/endpoint-application/{id}/workflow-detail`
- **请求方法**: `GET`
- **权限要求**: 需要登录，只能查看自己代理商的申请

## 请求参数

### 路径参数

| 参数名 | 类型 | 必填 | 说明 |
|:------|:-----|:-----|:-----|
| id | int | 是 | 申请ID |

## 返回示例

```json
{
  "ok": 1,
  "msg": "ok",
  "data": {
    "application_detail": {
      "id": 918,
      "top_agency": 150,
      "top_agency_name": "深圳读书郎教育科技有限公司",
      "second_agency": 0,
      "second_agency_name": "直营",
      "name": "读书郎专卖店(测试店)",
      "type": 3,
      "province": 110000,
      "city": 110100,
      "district": 110102,
      "address": "北京市西城区市政府大楼1层",
      "lng": "116.36600041099146",
      "lat": "39.91220117947085",
      "channel_level": 1,
      "blng": "116.372514",
      "blat": "39.918125",
      "policy_id": 14,
      "phone": "13560319968",
      "manager": "张三",
      "investor": "李四",
      "investor_phone": "13560319968",
      "expect_open_time": "2025-08-12",
      "position": "商业中心",
      "endpoint_area": "120平方米",
      "pay": "50000",
      "add_to_endpoint_id": 1001,
      "state": 200,
      "next_state": 300,
      "application_year": 2025,
      "created_at": "2025-01-15T10:30:00Z",
      "updated_at": "2025-01-15T14:20:00Z",
      "pics": [
        "rbcare/images/before_renovation_001.jpg",
        "rbcare/images/before_renovation_002.jpg"
      ],
      "extend": {
        "additional_info": "额外信息"
      },
      "code": 1001,
      "endpoint_id": 1001,
      "endpoint_code": "EP001",
      "created_at_formatted": "2025-01-15 10:30:00",
      "endpoint_type_name": "专卖店",
      "state_name": "物料支持完成",
      "material_data": {
        "material_list": [
          {
            "id": 1,
            "name": "投影仪",
            "quantity": 2,
            "price": 1500.00,
            "amount": 3000.00
          }
        ],
        "total_amount": 3000.00,
        "is_raw_format": false
      },
      "postback_data": {
        "write_off_table": "rbcare/documents/writeoff_table_001.pdf",
        "lease_contract": "rbcare/documents/lease_contract_001.pdf",
        "annual_rent": 120000.00,
        "design_renderings": ["rbcare/images/design_001.jpg"],
        "renovation_photos": ["rbcare/images/renovation_001.jpg"],
        "renovation_videos": ["rbcare/videos/renovation_001.mp4"],
        "diploma": ["rbcare/documents/diploma_001.pdf"],
        "endpoint_group_photo": ["rbcare/images/group_photo_001.jpg"],
        "extend": {"additional_info": "额外信息"}
      }
    },
    "workflow_steps": [
      {
        "state": 0,
        "name": "待审核",
        "type": "audit",
        "order": 1,
        "status": "completed",
        "status_text": "审核通过",
        "is_reached": true,
        "form_fields": [
          {
            "name": "audit_remark",
            "label": "审核备注",
            "type": "textarea",
            "required": false,
            "description": "审核意见或备注信息",
            "placeholder": "请输入审核意见..."
          }
        ],
        "form_data": {
          "audit_remark": "申请材料齐全，审核通过"
        },
        "actions": []
      },
      {
        "state": 100,
        "name": "审核通过",
        "type": "material",
        "order": 2,
        "status": "completed",
        "status_text": "已完成",
        "is_reached": true,
        "form_fields": [
          {
            "name": "material_list",
            "label": "物料清单",
            "type": "material_list",
            "required": true,
            "description": "选择需要申请的物料"
          },
          {
            "name": "need_material",
            "label": "是否需要物料支持",
            "type": "select",
            "required": true,
            "options": [
              {"value": "1", "label": "需要"},
              {"value": "0", "label": "不需要"}
            ]
          }
        ],
        "form_data": {
          "material_list": [
            {"id": 1, "name": "投影仪", "quantity": 2}
          ],
          "need_material": "1"
        },
        "actions": []
      },
      {
        "state": 200,
        "name": "物料支持完成",
        "type": "material_supported",
        "order": 3,
        "status": "current",
        "status_text": "进行中",
        "is_reached": true,
        "form_fields": [
          {
            "name": "design_renderings",
            "label": "设计效果图",
            "type": "file",
            "required": false,
            "description": "上传设计效果图"
          },
          {
            "name": "renovation_photos",
            "label": "装修实景图",
            "type": "file",
            "required": false,
            "description": "上传装修实景图片"
          },
          {
            "name": "renovation_videos",
            "label": "装修实景视频",
            "type": "file",
            "required": false,
            "description": "上传装修实景视频"
          }
        ],
        "form_data": {},
        "actions": [
          {
            "type": "postback",
            "label": "回传建店资料",
            "config": {}
          }
        ]
      },
      {
        "state": 300,
        "name": "核销已回传，待初审",
        "type": "write_off",
        "order": 4,
        "status": "waiting",
        "status_text": "等待中",
        "is_reached": false,
        "form_fields": [
          {
            "name": "write_off_table",
            "label": "核销表",
            "type": "file",
            "required": true,
            "description": "上传核销表文件"
          },
          {
            "name": "lease_contract",
            "label": "租赁合同",
            "type": "file",
            "required": false,
            "description": "上传租赁合同文件"
          },
          {
            "name": "annual_rent",
            "label": "年租金",
            "type": "number",
            "required": false,
            "description": "年租金金额",
            "validation": {
              "min": 0
            }
          }
        ],
        "form_data": {},
        "actions": []
      }
    ],
    "available_actions": [
      {
        "type": "postback",
        "label": "回传建店资料",
        "config": {}
      }
    ],
    "workflow_info": {
      "name": "常规建店工作流",
      "slug": "standard",
      "start_state": 0,
      "total_steps": 6
    }
  }
}
```

## 返回参数说明

### 主要字段

| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| application_detail | object | 申请详细信息（与原GetEndpointApplyDetail接口返回相同） |
| workflow_steps | array | 工作流步骤列表 |
| available_actions | array | 当前可执行的操作列表 |
| workflow_info | object | 工作流基本信息 |

### workflow_steps 字段说明

| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| state | int | 状态ID |
| name | string | 状态名称 |
| type | string | 状态类型（audit/material/write_off/material_supported/channel_audit） |
| order | int | 步骤顺序 |
| status | string | 步骤状态（completed/rejected/current/waiting） |
| status_text | string | 状态描述文本 |
| is_reached | bool | 是否已到达此节点 |
| form_fields | array | 该步骤需要的表单字段配置 |
| form_data | object | 该步骤已填写的数据 |
| actions | array | 该步骤可执行的操作（仅当前步骤有值） |

### form_fields 字段说明

| 字段名 | 类型 | 说明 |
|:------|:-----|:-----|
| name | string | 字段名称 |
| label | string | 字段标签 |
| type | string | 字段类型（text/textarea/select/file/date/number/material_list） |
| required | bool | 是否必填 |
| options | array | 选项列表（select类型使用） |
| validation | object | 验证规则 |
| description | string | 字段描述 |
| placeholder | string | 占位符 |

## 使用场景

1. **工作流进度展示**: 显示申请在整个工作流中的进度
2. **表单动态渲染**: 根据当前状态动态显示需要填写的表单
3. **操作按钮控制**: 根据当前状态显示可执行的操作
4. **数据回显**: 显示每个步骤已填写的数据

## 注意事项

1. 只能查看自己代理商的申请
2. `is_reached` 字段表示流程是否已经到达该节点
3. `form_data` 只包含该步骤相关的数据
4. `actions` 只有当前步骤才会有值
5. 表单字段配置根据状态类型动态生成

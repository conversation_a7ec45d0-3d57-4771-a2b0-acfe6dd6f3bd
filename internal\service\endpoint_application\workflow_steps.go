package endpoint_application

import (
	"encoding/json"
	"marketing/internal/api/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/statemachine"
	"strconv"
	"time"
)

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	State      int    `json:"state"`
	Name       string `json:"name"`
	Order      int    `json:"order"`
	Status     string `json:"status"`      // completed, rejected, current, waiting
	StatusText string `json:"status_text"` // "审核通过", "已完成", "核销不通过"等
}

// buildWorkflowSteps 根据当前状态构建工作流步骤
func (e endpointApplyService) buildWorkflowSteps(currentState int, workflow *statemachine.WorkflowConfig) []*WorkflowStep {
	// 从工作流配置构建主流程路径
	mainPath := e.buildMainPath(workflow)

	var steps []*WorkflowStep
	for i, stepState := range mainPath {
		stateKey := strconv.Itoa(stepState)
		stateNode, exists := workflow.States[stateKey]
		if !exists {
			continue
		}

		step := &WorkflowStep{
			State:      stepState,
			Name:       stateNode.Name,
			Order:      i + 1,
			Status:     e.getStepStatus(stepState, currentState),
			StatusText: e.getStatusText(stepState, currentState),
		}
		steps = append(steps, step)
	}

	return steps
}

// buildMainPath 构建主流程路径
func (e endpointApplyService) buildMainPath(workflow *statemachine.WorkflowConfig) []int {
	mainPath := []int{}
	visited := make(map[int]bool)
	currentState := int(workflow.StartState)

	for !visited[currentState] && len(mainPath) < 10 {
		mainPath = append(mainPath, currentState)
		visited[currentState] = true

		stateKey := strconv.Itoa(currentState)
		if stateNode, exists := workflow.States[stateKey]; exists {
			// 找下一个正向状态（优先approve操作）
			nextState := -1
			for _, action := range []string{"approve", "submit_material", "postback"} {
				if next, ok := stateNode.Transitions[action]; ok && int(next) > 0 {
					nextState = int(next)
					break
				}
			}
			if nextState == -1 || visited[nextState] {
				break
			}
			currentState = nextState
		} else {
			break
		}
	}

	return mainPath
}

// getStepStatus 获取步骤状态
func (e endpointApplyService) getStepStatus(stepState, currentState int) string {
	switch {
	case currentState == stepState:
		return "current"
	case e.isStepCompleted(stepState, currentState):
		return "completed"
	case e.isStepRejected(stepState, currentState):
		return "rejected"
	default:
		return "waiting"
	}
}

// isStepCompleted 判断步骤是否已完成
func (e endpointApplyService) isStepCompleted(stepState, currentState int) bool {
	if currentState < 0 {
		// 负数状态：比如-400说明到过300但在400被拒绝
		absCurrentState := -currentState
		return stepState < absCurrentState
	}
	// 正常状态：当前状态>=步骤状态就是完成了
	return currentState >= stepState
}

// isStepRejected 判断步骤是否被拒绝
func (e endpointApplyService) isStepRejected(stepState, currentState int) bool {
	switch stepState {
	case 100:
		return currentState == -100
	case 400:
		return currentState == -400
	default:
		return false
	}
}

// getStatusText 获取状态文本
func (e endpointApplyService) getStatusText(stepState, currentState int) string {
	status := e.getStepStatus(stepState, currentState)

	switch status {
	case "completed":
		if stepState == 0 {
			return "审核通过"
		}
		return "已完成"
	case "rejected":
		if stepState == 100 {
			return "审核不通过"
		} else if stepState == 400 {
			return "核销不通过"
		}
		return "审核不通过"
	case "current":
		return "进行中"
	default:
		return "等待中"
	}
}

// buildWorkflowStepsWithForm 构建带表单字段的工作流步骤
func (e endpointApplyService) buildWorkflowStepsWithForm(currentState int, workflow *statemachine.WorkflowConfig, apply *model.EndpointApplication) ([]*endpoint_application.WorkflowStepWithForm, error) {
	// 从工作流配置构建主流程路径
	mainPath := e.buildMainPath(workflow)

	var steps []*endpoint_application.WorkflowStepWithForm
	for i, stepState := range mainPath {
		stateKey := strconv.Itoa(stepState)
		stateNode, exists := workflow.States[stateKey]
		if !exists {
			continue
		}

		// 构建基本步骤信息
		step := &endpoint_application.WorkflowStepWithForm{
			State:      stepState,
			Name:       stateNode.Name,
			Type:       stateNode.Type,
			Order:      i + 1,
			Status:     e.getStepStatus(stepState, currentState),
			StatusText: e.getStatusText(stepState, currentState),
			IsReached:  e.isStepCompleted(stepState, currentState) || currentState == stepState,
		}

		// 获取该步骤的操作记录数据
		operationData, err := e.getFormDataForState(stepState, apply)
		if err != nil {
			return nil, err
		}
		step.OperationData = operationData

		// 获取该步骤可执行的操作
		actions := e.getActionsForState(stateNode, stepState, currentState)
		step.Actions = actions

		steps = append(steps, step)
	}

	return steps, nil
}



// getFormDataForState 获取状态对应的实际操作记录数据
func (e endpointApplyService) getFormDataForState(stepState int, apply *model.EndpointApplication) (map[string]interface{}, error) {
	formData := make(map[string]interface{})

	switch stepState {
	case 0: // 待审核状态 - 获取审核记录
		if apply.AuditMan != "" {
			formData["audit_man"] = apply.AuditMan
		}
		auditTime := time.Time(apply.AuditTime)
		if !auditTime.IsZero() {
			formData["audit_time"] = auditTime.Format("2006-01-02 15:04:05")
		}
		if apply.AuditAdvice != "" {
			formData["audit_advice"] = apply.AuditAdvice
		}
		// 根据当前状态判断审核结果
		if apply.State == 100 {
			formData["audit_result"] = "approved"
			formData["audit_result_text"] = "审核通过"
		} else if apply.State == -100 {
			formData["audit_result"] = "rejected"
			formData["audit_result_text"] = "审核不通过"
		}

	case 100: // 审核通过，物料支持状态 - 获取物料支持记录
		// 从物料支持表获取数据
		materials, err := e.repo.GetEndpointMaterialSupportById(nil, apply.ID)
		if err == nil && len(materials) > 0 {
			var materialList []map[string]interface{}
			var totalAmount float64
			for _, material := range materials {
				item := map[string]interface{}{
					"id":       material.ID,
					"name":     material.Name,
					"quantity": material.Num,
					"price":    material.Price,
					"amount":   float64(material.Num) * material.Price,
				}
				materialList = append(materialList, item)
				totalAmount += float64(material.Num) * material.Price
			}
			formData["material_list"] = materialList
			formData["total_amount"] = totalAmount
			formData["material_support_time"] = materials[0].CreatedAt.Format("2006-01-02 15:04:05")
		}

		// 物料支持金额
		if apply.BagSupportAmount != nil {
			formData["bag_support_amount"] = *apply.BagSupportAmount
		}

	case 200: // 物料支持完成状态 - 获取回传记录
		// 从回传表获取数据
		postback, err := e.repo.GetEndpointApplicationPostbackById(nil, apply.ID)
		if err == nil && postback != nil {
			formData["write_off_table"] = postback.WriteOffTable
			formData["lease_contract"] = postback.LeaseContract
			formData["annual_rent"] = postback.AnnualRent
			formData["confirm_date"] = postback.ConfirmDate.Format("2006-01-02 15:04:05")

			// 解析JSON字段
			var designRenderings []string
			var renovationPhotos []string
			var renovationVideos []string
			var diploma []string
			var endpointGroupPhoto []string

			json.Unmarshal([]byte(postback.DesignRenderings), &designRenderings)
			json.Unmarshal([]byte(postback.RenovationPhotos), &renovationPhotos)
			json.Unmarshal([]byte(postback.RenovationVideos), &renovationVideos)
			json.Unmarshal([]byte(postback.Diploma), &diploma)
			json.Unmarshal([]byte(postback.EndpointGroupPhoto), &endpointGroupPhoto)

			formData["design_renderings"] = designRenderings
			formData["renovation_photos"] = renovationPhotos
			formData["renovation_videos"] = renovationVideos
			formData["diploma"] = diploma
			formData["endpoint_group_photo"] = endpointGroupPhoto
		}

	case 300: // 核销已回传，待初审状态 - 获取核销记录
		if apply.WriteOffMan != nil && *apply.WriteOffMan != "" {
			formData["write_off_man"] = *apply.WriteOffMan
		}
		writeOffTime := time.Time(apply.WriteOffTime)
		if !writeOffTime.IsZero() {
			formData["write_off_time"] = writeOffTime.Format("2006-01-02 15:04:05")
		}
		if apply.WriteOffAdvice != "" {
			formData["write_off_advice"] = apply.WriteOffAdvice
		}
		// 根据当前状态判断核销结果
		if apply.State == 400 {
			formData["write_off_result"] = "approved"
			formData["write_off_result_text"] = "初审通过"
		} else if apply.State == -400 {
			formData["write_off_result"] = "rejected"
			formData["write_off_result_text"] = "初审不通过"
		}

	case 400: // 初审通过，待渠道审核状态 - 获取渠道审核记录
		// 可以从扩展字段或状态变更记录中获取渠道审核信息
		if apply.Extend != "" {
			var extendData map[string]interface{}
			if err := json.Unmarshal([]byte(apply.Extend), &extendData); err == nil {
				if channelAuditMan, exists := extendData["channel_audit_man"]; exists {
					formData["channel_audit_man"] = channelAuditMan
				}
				if channelAuditTime, exists := extendData["channel_audit_time"]; exists {
					formData["channel_audit_time"] = channelAuditTime
				}
				if channelAuditAdvice, exists := extendData["channel_audit_advice"]; exists {
					formData["channel_audit_advice"] = channelAuditAdvice
				}
			}
		}
		// 根据当前状态判断渠道审核结果
		if apply.State == 500 {
			formData["channel_audit_result"] = "approved"
			formData["channel_audit_result_text"] = "渠道审核通过"
		} else if apply.State == -500 {
			formData["channel_audit_result"] = "rejected"
			formData["channel_audit_result_text"] = "渠道审核不通过"
		}

	case 500: // 渠道审核通过，流程完成
		formData["completed"] = true
		updatedAt := time.Time(apply.UpdatedAt)
		if !updatedAt.IsZero() {
			formData["completed_time"] = updatedAt.Format("2006-01-02 15:04:05")
		}
	}

	return formData, nil
}

// getActionsForState 获取状态对应的可执行操作
func (e endpointApplyService) getActionsForState(stateNode *statemachine.StateNode, stepState, currentState int) []endpoint_application.ActionInfo {
	var actions []endpoint_application.ActionInfo

	// 只有当前状态才显示可执行的操作
	if currentState != stepState {
		return actions
	}

	// 转换状态机的操作为API格式
	for _, action := range stateNode.Actions {
		actionInfo := endpoint_application.ActionInfo{
			Type:   action.Type,
			Label:  action.Label,
			Config: action.Config,
		}
		actions = append(actions, actionInfo)
	}

	return actions
}

package endpoint_application

import (
	"encoding/json"
	"marketing/internal/api/endpoint_application"
	"marketing/internal/model"
	"marketing/internal/pkg/statemachine"
	"strconv"
)

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	State      int    `json:"state"`
	Name       string `json:"name"`
	Order      int    `json:"order"`
	Status     string `json:"status"`      // completed, rejected, current, waiting
	StatusText string `json:"status_text"` // "审核通过", "已完成", "核销不通过"等
}

// buildWorkflowSteps 根据当前状态构建工作流步骤
func (e endpointApplyService) buildWorkflowSteps(currentState int, workflow *statemachine.WorkflowConfig) []*WorkflowStep {
	// 从工作流配置构建主流程路径
	mainPath := e.buildMainPath(workflow)

	var steps []*WorkflowStep
	for i, stepState := range mainPath {
		stateKey := strconv.Itoa(stepState)
		stateNode, exists := workflow.States[stateKey]
		if !exists {
			continue
		}

		step := &WorkflowStep{
			State:      stepState,
			Name:       stateNode.Name,
			Order:      i + 1,
			Status:     e.getStepStatus(stepState, currentState),
			StatusText: e.getStatusText(stepState, currentState),
		}
		steps = append(steps, step)
	}

	return steps
}

// buildMainPath 构建主流程路径
func (e endpointApplyService) buildMainPath(workflow *statemachine.WorkflowConfig) []int {
	mainPath := []int{}
	visited := make(map[int]bool)
	currentState := int(workflow.StartState)

	for !visited[currentState] && len(mainPath) < 10 {
		mainPath = append(mainPath, currentState)
		visited[currentState] = true

		stateKey := strconv.Itoa(currentState)
		if stateNode, exists := workflow.States[stateKey]; exists {
			// 找下一个正向状态（优先approve操作）
			nextState := -1
			for _, action := range []string{"approve", "submit_material", "postback"} {
				if next, ok := stateNode.Transitions[action]; ok && int(next) > 0 {
					nextState = int(next)
					break
				}
			}
			if nextState == -1 || visited[nextState] {
				break
			}
			currentState = nextState
		} else {
			break
		}
	}

	return mainPath
}

// getStepStatus 获取步骤状态
func (e endpointApplyService) getStepStatus(stepState, currentState int) string {
	switch {
	case currentState == stepState:
		return "current"
	case e.isStepCompleted(stepState, currentState):
		return "completed"
	case e.isStepRejected(stepState, currentState):
		return "rejected"
	default:
		return "waiting"
	}
}

// isStepCompleted 判断步骤是否已完成
func (e endpointApplyService) isStepCompleted(stepState, currentState int) bool {
	if currentState < 0 {
		// 负数状态：比如-400说明到过300但在400被拒绝
		absCurrentState := -currentState
		return stepState < absCurrentState
	}
	// 正常状态：当前状态>=步骤状态就是完成了
	return currentState >= stepState
}

// isStepRejected 判断步骤是否被拒绝
func (e endpointApplyService) isStepRejected(stepState, currentState int) bool {
	switch stepState {
	case 100:
		return currentState == -100
	case 400:
		return currentState == -400
	default:
		return false
	}
}

// getStatusText 获取状态文本
func (e endpointApplyService) getStatusText(stepState, currentState int) string {
	status := e.getStepStatus(stepState, currentState)

	switch status {
	case "completed":
		if stepState == 0 {
			return "审核通过"
		}
		return "已完成"
	case "rejected":
		if stepState == 100 {
			return "审核不通过"
		} else if stepState == 400 {
			return "核销不通过"
		}
		return "审核不通过"
	case "current":
		return "进行中"
	default:
		return "等待中"
	}
}

// buildWorkflowStepsWithForm 构建带表单字段的工作流步骤
func (e endpointApplyService) buildWorkflowStepsWithForm(currentState int, workflow *statemachine.WorkflowConfig, apply *model.EndpointApplication) ([]*endpoint_application.WorkflowStepWithForm, error) {
	// 从工作流配置构建主流程路径
	mainPath := e.buildMainPath(workflow)

	var steps []*endpoint_application.WorkflowStepWithForm
	for i, stepState := range mainPath {
		stateKey := strconv.Itoa(stepState)
		stateNode, exists := workflow.States[stateKey]
		if !exists {
			continue
		}

		// 构建基本步骤信息
		step := &endpoint_application.WorkflowStepWithForm{
			State:      stepState,
			Name:       stateNode.Name,
			Type:       stateNode.Type,
			Order:      i + 1,
			Status:     e.getStepStatus(stepState, currentState),
			StatusText: e.getStatusText(stepState, currentState),
			IsReached:  e.isStepCompleted(stepState, currentState) || currentState == stepState,
		}

		// 获取该步骤的表单字段配置
		formFields, err := e.getFormFieldsForState(stateNode)
		if err != nil {
			return nil, err
		}
		step.FormFields = formFields

		// 获取该步骤已填写的数据
		formData, err := e.getFormDataForState(stepState, apply)
		if err != nil {
			return nil, err
		}
		step.FormData = formData

		// 获取该步骤可执行的操作
		actions := e.getActionsForState(stateNode, stepState, currentState)
		step.Actions = actions

		steps = append(steps, step)
	}

	return steps, nil
}

// getFormFieldsForState 获取状态对应的表单字段配置
func (e endpointApplyService) getFormFieldsForState(stateNode *statemachine.StateNode) ([]endpoint_application.FormFieldConfig, error) {
	var formFields []endpoint_application.FormFieldConfig

	switch stateNode.Type {
	case "audit":
		// 审核状态的表单字段
		formFields = []endpoint_application.FormFieldConfig{
			{
				Name:        "audit_remark",
				Label:       "审核备注",
				Type:        "textarea",
				Required:    false,
				Description: "审核意见或备注信息",
				Placeholder: "请输入审核意见...",
			},
		}
	case "material":
		// 物料支持状态的表单字段
		formFields = []endpoint_application.FormFieldConfig{
			{
				Name:        "material_list",
				Label:       "物料清单",
				Type:        "material_list",
				Required:    true,
				Description: "选择需要申请的物料",
			},
			{
				Name:        "need_material",
				Label:       "是否需要物料支持",
				Type:        "select",
				Required:    true,
				Options: []endpoint_application.FieldOption{
					{Value: "1", Label: "需要"},
					{Value: "0", Label: "不需要"},
				},
			},
		}
	case "write_off":
		// 核销状态的表单字段
		formFields = []endpoint_application.FormFieldConfig{
			{
				Name:        "write_off_table",
				Label:       "核销表",
				Type:        "file",
				Required:    true,
				Description: "上传核销表文件",
			},
			{
				Name:        "lease_contract",
				Label:       "租赁合同",
				Type:        "file",
				Required:    false,
				Description: "上传租赁合同文件",
			},
			{
				Name:        "annual_rent",
				Label:       "年租金",
				Type:        "number",
				Required:    false,
				Description: "年租金金额",
				Validation: &endpoint_application.FieldValidation{
					Min: func() *float64 { v := 0.0; return &v }(),
				},
			},
		}
	case "material_supported":
		// 物料支持完成状态的表单字段
		formFields = []endpoint_application.FormFieldConfig{
			{
				Name:        "design_renderings",
				Label:       "设计效果图",
				Type:        "file",
				Required:    false,
				Description: "上传设计效果图",
			},
			{
				Name:        "renovation_photos",
				Label:       "装修实景图",
				Type:        "file",
				Required:    false,
				Description: "上传装修实景图片",
			},
			{
				Name:        "renovation_videos",
				Label:       "装修实景视频",
				Type:        "file",
				Required:    false,
				Description: "上传装修实景视频",
			},
		}
	case "channel_audit":
		// 渠道审核状态的表单字段
		formFields = []endpoint_application.FormFieldConfig{
			{
				Name:        "channel_audit_remark",
				Label:       "渠道审核备注",
				Type:        "textarea",
				Required:    false,
				Description: "渠道审核意见",
				Placeholder: "请输入渠道审核意见...",
			},
		}
	}

	return formFields, nil
}

// getFormDataForState 获取状态对应的已填写数据
func (e endpointApplyService) getFormDataForState(stepState int, apply *model.EndpointApplication) (map[string]interface{}, error) {
	formData := make(map[string]interface{})

	// 解析扩展字段
	if apply.Extend != "" {
		var extendData map[string]interface{}
		if err := json.Unmarshal([]byte(apply.Extend), &extendData); err == nil {
			// 根据状态类型提取相关数据
			switch stepState {
			case 0: // 待审核
				if auditRemark, exists := extendData["audit_remark"]; exists {
					formData["audit_remark"] = auditRemark
				}
			case 100: // 审核通过，物料支持
				if materialList, exists := extendData["material_list"]; exists {
					formData["material_list"] = materialList
				}
				if needMaterial, exists := extendData["need_material"]; exists {
					formData["need_material"] = needMaterial
				}
			case 300: // 核销已回传
				if writeOffTable, exists := extendData["write_off_table"]; exists {
					formData["write_off_table"] = writeOffTable
				}
				if leaseContract, exists := extendData["lease_contract"]; exists {
					formData["lease_contract"] = leaseContract
				}
				if annualRent, exists := extendData["annual_rent"]; exists {
					formData["annual_rent"] = annualRent
				}
			case 200: // 物料支持完成
				if designRenderings, exists := extendData["design_renderings"]; exists {
					formData["design_renderings"] = designRenderings
				}
				if renovationPhotos, exists := extendData["renovation_photos"]; exists {
					formData["renovation_photos"] = renovationPhotos
				}
				if renovationVideos, exists := extendData["renovation_videos"]; exists {
					formData["renovation_videos"] = renovationVideos
				}
			}
		}
	}

	return formData, nil
}

// getActionsForState 获取状态对应的可执行操作
func (e endpointApplyService) getActionsForState(stateNode *statemachine.StateNode, stepState, currentState int) []endpoint_application.ActionInfo {
	var actions []endpoint_application.ActionInfo

	// 只有当前状态才显示可执行的操作
	if currentState != stepState {
		return actions
	}

	// 转换状态机的操作为API格式
	for _, action := range stateNode.Actions {
		actionInfo := endpoint_application.ActionInfo{
			Type:   action.Type,
			Label:  action.Label,
			Config: action.Config,
		}
		actions = append(actions, actionInfo)
	}

	return actions
}

package endpoint_application

import (
	api "marketing/internal/api/endpoint_application"
	"marketing/internal/handler"
	"marketing/internal/pkg/errors"
	service "marketing/internal/service/endpoint_application"
	userSvc "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
	"github.com/spf13/cast"
)

type EndpointApplyHandler interface {
	CreateEndpointApply(c *gin.Context)
	UpdateEndpointApply(c *gin.Context)
	GetEndpointApplyList(c *gin.Context)
	GetEndpointApplyDetail(c *gin.Context)
	PostbackEndpointApply(c *gin.Context)
	DownloadWriteOffTable(c *gin.Context)
	// 工作流相关接口
	GetAvailableActions(c *gin.Context)
	TransitionState(c *gin.Context)
	GetWorkflowSteps(c *gin.Context)
	// 新增整合接口
	GetWorkflowDetailWithSteps(c *gin.Context)
}

type endpointApply struct {
	applyService service.EndpointApplyService
	userService  userSvc.AdminUserInterface
}

func NewEndpointApplyHandler(applyService service.EndpointApplyService, userService userSvc.AdminUserInterface) EndpointApplyHandler {
	return &endpointApply{
		applyService: applyService,
		userService:  userService,
	}
}

func (e endpointApply) CreateEndpointApply(c *gin.Context) {
	var req api.CreatedEndpointApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	//参数处理 - OssPath类型会自动处理前缀，无需手动调用DeleteSlicePrefix

	err := e.applyService.CreateEndpointApply(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

// GetWorkflowSteps 获取工作流步骤
func (e endpointApply) GetWorkflowSteps(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}

	steps, err := e.applyService.GetWorkflowSteps(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, steps)
}

// GetWorkflowDetailWithSteps 获取工作流详情和步骤（整合接口）
func (e endpointApply) GetWorkflowDetailWithSteps(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}

	// 验证用户权限（确保只能查看自己代理商的申请）
	uid := c.GetUint("uid")
	if uid == 0 && gin.Mode() == gin.DebugMode {
		uid = cast.ToUint(c.Query("uid"))
	}
	agency, err := e.userService.GetUserAgency(c, uid)
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, errors.NewErr("用户代理不存在"))
		return
	}

	detail, err := e.applyService.GetWorkflowDetailWithSteps(c, id, agency.ID, agency.Level)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, detail)
}

func (e endpointApply) UpdateEndpointApply(c *gin.Context) {
	id := cast.ToInt(c.Param("id"))
	if id <= 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	var req api.CreatedEndpointApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	//参数处理 - OssPath类型会自动处理前缀，无需手动调用DeleteSlicePrefix
	err := e.applyService.UpdateEndpointApply(c, id, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (e endpointApply) GetEndpointApplyList(c *gin.Context) {
	var req api.EndpointApplicationListReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.SetDefaults()
	//获取用户代理
	uid := c.GetUint("uid")
	if uid == 0 && gin.Mode() == gin.DebugMode {
		uid = cast.ToUint(c.Query("uid"))
	}
	agency, err := e.userService.GetUserAgency(c, uid)
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, errors.NewErr("用户代理不存在"))
		return
	}
	if agency.Level == 1 {
		req.TopAgency = int(agency.ID)
	} else {
		req.SecondAgency = int(agency.ID)
	}
	data, total, err := e.applyService.GetEndpointApplyList(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c, data, total)
}

// GetEndpointApplyDetail 获取申请详情
func (e endpointApply) GetEndpointApplyDetail(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}

	// 验证用户权限（确保只能查看自己代理商的申请）
	uid := c.GetUint("uid")
	if uid == 0 && gin.Mode() == gin.DebugMode {
		uid = cast.ToUint(c.Query("uid"))
	}
	agency, err := e.userService.GetUserAgency(c, uid)
	if err != nil || agency == nil || agency.ID == 0 {
		handler.Error(c, errors.NewErr("用户代理不存在"))
		return
	}

	detail, err := e.applyService.GetEndpointApplyDetail(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	// 验证权限：确保申请属于当前用户的代理商
	if agency.Level == 1 {
		// 一级代理商，检查top_agency
		if detail.TopAgency != agency.ID {
			handler.Error(c, errors.NewErr("无权限查看此申请"))
			return
		}
	} else {
		// 二级代理商，检查second_agency
		if detail.SecondAgency != agency.ID {
			handler.Error(c, errors.NewErr("无权限查看此申请"))
			return
		}
	}

	handler.Success(c, detail)
}

func (e endpointApply) PostbackEndpointApply(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	var req api.PostbackEndpointApplyReq
	if err := c.ShouldBind(&req); err != nil {
		handler.Error(c, err)
		return
	}
	req.ID = id

	// 使用工作流模式进行状态转换
	stateReq := api.StateTransitionReq{
		ID:     id,
		Action: "postback", // 回传建店资料的操作
		Data: map[string]interface{}{
			"write_off_table":      req.WriteOffTable,
			"lease_contract":       req.LeaseContract,
			"annual_rent":          req.AnnualRent,
			"design_renderings":    req.DesignRenderings,
			"renovation_photos":    req.RenovationPhotos,
			"renovation_videos":    req.RenovationVideos,
			"diploma":              req.Diploma,
			"endpoint_group_photo": req.EndpointGroupPhoto,
			"extend":               req.Extend,
			"remark":               req.Remark,
		},
	}

	err := e.applyService.TransitionState(c, &stateReq)
	if err != nil {
		handler.Error(c, err)
		return
	}
	handler.Success(c)
}

func (e endpointApply) DownloadWriteOffTable(c *gin.Context) {
	id := cast.ToInt(c.Param("id"))
	if id <= 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	// 下载
	err := e.applyService.DownloadWriteOffTable(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}
}

// GetAvailableActions 获取可用操作
func (e endpointApply) GetAvailableActions(c *gin.Context) {
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}

	actions, err := e.applyService.GetAvailableActions(c, id)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c, actions)
}

// TransitionState 状态转换
func (e endpointApply) TransitionState(c *gin.Context) {
	var req api.StateTransitionReq
	if err := c.ShouldBindJSON(&req); err != nil {
		handler.Error(c, err)
		return
	}

	// 从URL参数获取ID
	id := cast.ToUint(c.Param("id"))
	if id == 0 {
		handler.Error(c, errors.NewErr("id参数错误"))
		return
	}
	req.ID = id

	err := e.applyService.TransitionState(c, &req)
	if err != nil {
		handler.Error(c, err)
		return
	}

	handler.Success(c)
}

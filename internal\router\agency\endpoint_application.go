package agency

import (
	"marketing/internal/dao"
	"marketing/internal/dao/endpoint"
	repo "marketing/internal/dao/endpoint_application"
	"marketing/internal/dao/warranty"
	"marketing/internal/handler/agency/endpoint_application"
	handlers "marketing/internal/handler/agency/endpoint_application"
	"marketing/internal/pkg/db"
	service "marketing/internal/service/endpoint_application"
	service2 "marketing/internal/service/system"

	"github.com/gin-gonic/gin"
)

type EndpointApplicationRouter struct {
	handler endpoint_application.EndpointApplyHandler
}

func NewEndpointApplicationRouter(userService service2.AdminUserInterface) *EndpointApplicationRouter {
	var Db = db.GetDB()
	endpointApplyDao := repo.NewEndpointApplyDao(Db)
	policyDao := repo.NewEndpointPolicyDao(Db)
	endpointDao := endpoint.NewEndpointDao(Db)
	endpointTypeRepo := dao.NewEndpointTypeRepository(Db)
	configRepo := dao.NewConfigRepository(Db)
	subjectRepo := dao.NewGormTrainSubjectDao(Db)
	materialRepo := dao.NewMaterialDao()
	endpointImageDao := endpoint.NewEndpointImageDao(Db)
	warrantyRepo := warranty.NewWarrantyDao(Db)
	workflowRepo := repo.NewWorkflowConfigDao(Db)

	endpointApplyService := service.NewEndpointApplyService(endpointApplyDao, policyDao, endpointDao, endpointTypeRepo, configRepo, subjectRepo, materialRepo, endpointImageDao, warrantyRepo, workflowRepo)
	handler := handlers.NewEndpointApplyHandler(endpointApplyService, userService)

	return &EndpointApplicationRouter{
		handler: handler,
	}
}

func (e *EndpointApplicationRouter) Register(r *gin.RouterGroup) {
	r.POST("", e.handler.CreateEndpointApply)
	r.PUT("/:id", e.handler.UpdateEndpointApply)
	r.GET("", e.handler.GetEndpointApplyList)
	r.GET("/:id", e.handler.GetEndpointApplyDetail)
	r.POST("/:id/postback", e.handler.PostbackEndpointApply)
	r.GET("/:id/write-off-table", e.handler.DownloadWriteOffTable)

	// 工作流相关路由
	r.GET("/:id/actions", e.handler.GetAvailableActions)
	r.POST("/:id/transition", e.handler.TransitionState)
	r.GET("/:id/steps", e.handler.GetWorkflowSteps)
	r.GET("/:id/workflow-detail", e.handler.GetWorkflowDetailWithSteps)
}
